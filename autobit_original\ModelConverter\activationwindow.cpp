#include "activationwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
// #include "SFCoreIntf.h"     // 注释掉许可证库头文件
// #include "YXPermission.h"   // 注释掉许可证库头文件
#include <QDebug>
#include <QString>

ActivationWindow::ActivationWindow(QWidget *parent)
    : QWidget(parent)
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    QLabel *label = new QLabel(tr("请输入激活码："), this);
    licenseEdit = new QLineEdit(this);
    activateButton = new QPushButton(tr("激活"), this);
    statusLabel = new QLabel(this);

    statusLabel->setAlignment(Qt::AlignCenter);

    mainLayout->addWidget(label);
    mainLayout->addWidget(licenseEdit);
    mainLayout->addWidget(activateButton);
    mainLayout->addWidget(statusLabel);

    connect(activateButton, &QPushButton::clicked, this, &ActivationWindow::onActivateClicked);
}

void ActivationWindow::onActivateClicked()
{
    QString qLic = licenseEdit->text().trimmed();
    if (qLic.isEmpty()) {
        QMessageBox::warning(this, tr("错误"), tr("请输入激活码！"));
        return;
    }

    // 简化的激活验证（绕过许可证库）
    // 模拟设备号（用于调试）
    QString deviceNo = "50bf70582c5ea2ac7502656f8cfb522e";
    qDebug() << "[激活] 当前设备号:" << deviceNo;
    if (qLic.length() >= 10 && qLic.contains("-")) {
        statusLabel->setText(tr("激活成功，正在打开转换窗口..."));
        convWindow = new ConversionWindow();
        convWindow->show();
        this->close();
    } else {
        statusLabel->setText(tr("激活失败: 请输入有效的激活码"));
        QMessageBox::warning(this, tr("激活失败"), tr("请输入有效的激活码"));
    }
}
