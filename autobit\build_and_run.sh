#!/bin/bash
# AutoBit 模型转换工具 - 编译启动脚本
# 更新时间: 2024-06-29
# 支持双模式激活系统和现代化UI

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }
echo_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }

# 脚本信息
echo_info "=== AutoBit 模型转换工具 - 编译启动脚本 ==="
echo_info "版本: 2.0.0"
echo_info "更新时间: 2024-06-29"
echo_info "特性: 双模式激活系统 + 现代化UI + 硬件绑定"
echo_info ""

# 检查运行环境
echo_step "1. 检查运行环境"

# 检查是否在Linux环境中
if ! uname -a | grep -q "Linux"; then
    echo_error "此脚本需要在Linux环境中运行"
    echo_info "请使用以下命令进入WSL2环境:"
    echo_info "wsl -d Ubuntu-20.04"
    exit 1
fi

echo_success "Linux环境检查通过: $(uname -a)"

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    echo_error "Docker未安装或不可用，请确保Docker Desktop已启动"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo_error "Docker服务未运行，请启动Docker Desktop"
    exit 1
fi

echo_success "Docker环境检查通过"

# 检查和加载Docker镜像
echo_step "2. 检查Docker镜像"

# 检查是否存在model-converter镜像
if docker images | grep -q "model-converter"; then
    echo_success "Docker镜像 model-converter 已存在"
    docker images | grep model-converter
else
    echo_warning "未找到 model-converter 镜像"

    # 查找镜像文件
    if [ -f "../model-converter.tar" ]; then
        echo_info "找到镜像文件 model-converter.tar，正在加载..."
        docker load -i ../model-converter.tar
        if [ $? -eq 0 ]; then
            echo_success "Docker镜像加载成功"
        else
            echo_error "Docker镜像加载失败"
            exit 1
        fi
    else
        echo_warning "未找到 model-converter.tar 文件"
        echo_info "请确保以下文件之一存在："
        echo_info "  - model-converter.tar (在当前目录)"
        echo_info "  - 或者已经通过其他方式加载了 model-converter 镜像"
        echo_info ""
        echo_info "如果没有镜像文件，模型转换功能将无法使用"
        echo_info "但Qt界面仍可正常启动进行激活测试"
    fi
fi

# 检查Qt环境
echo_step "3. 检查Qt开发环境"

# Qt路径配置
QT_DIR="/opt/Qt5.12.6/5.12.6/gcc_64"
QT_BIN_DIR="$QT_DIR/bin"
QT_LIB_DIR="$QT_DIR/lib"

if [ ! -d "$QT_DIR" ]; then
    echo_error "Qt安装目录不存在: $QT_DIR"
    echo_info "请确保Qt 5.12.6已正确安装到指定目录"
    exit 1
fi

if [ ! -f "$QT_BIN_DIR/qmake" ]; then
    echo_error "qmake不存在: $QT_BIN_DIR/qmake"
    exit 1
fi

echo_success "Qt环境检查通过: $QT_DIR"

# 设置Qt环境变量
export PATH="$QT_BIN_DIR:$PATH"
export LD_LIBRARY_PATH="$QT_LIB_DIR:$LD_LIBRARY_PATH"
export QT_QPA_PLATFORM_PLUGIN_PATH="$QT_LIB_DIR/plugins"

echo_info "Qt版本: $($QT_BIN_DIR/qmake --version | grep Qt)"

# 检查X11环境（GUI支持）
echo_step "4. 检查X11显示环境"

if [ -z "$DISPLAY" ]; then
    echo_warning "未设置DISPLAY环境变量"
    echo_info "正在自动设置DISPLAY变量..."
    export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
    echo_info "DISPLAY已设置为: $DISPLAY"
    echo_warning "请确保Windows中的VcXsrv X11服务器正在运行"
else
    echo_success "DISPLAY环境变量已设置: $DISPLAY"
fi

# 设置字体环境变量，解决中文字体显示问题
export QT_QPA_FONTDIR="/usr/share/fonts"
export FONTCONFIG_PATH="/etc/fonts"

echo_success "字体环境配置完成"

# 测试X11连接 - 使用简单的环境变量检查
if [ -n "$DISPLAY" ]; then
    echo_success "X11环境变量已设置: $DISPLAY"
else
    echo_warning "X11环境变量未设置，GUI可能无法显示"
    echo_info "请设置DISPLAY变量，例如: export DISPLAY=:0"
fi

# 进入项目目录
echo_step "5. 准备项目环境"

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MODEL_CONVERTER_DIR="$PROJECT_DIR/ModelConverter"

if [ ! -d "$MODEL_CONVERTER_DIR" ]; then
    echo_error "ModelConverter目录不存在: $MODEL_CONVERTER_DIR"
    exit 1
fi

cd "$MODEL_CONVERTER_DIR"
echo_success "已进入项目目录: $MODEL_CONVERTER_DIR"

# 检查必要文件
REQUIRED_FILES=(
    "ModelConverter.pro"
    "main.cpp"
    "activationwindow.h"
    "activationwindow.cpp"
    "conversionwindow.h"
    "conversionwindow.cpp"
    "HardwareFingerprint.h"
    "HardwareFingerprint.cpp"
    "LicenseManager.h"
    "LicenseManager.cpp"
    "libsf-core-ls.so"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo_error "必要文件不存在: $file"
        exit 1
    fi
done

echo_success "项目文件检查完成"

# 创建构建目录
echo_step "6. 准备构建环境"

BUILD_DIR="build"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 创建必要的子目录
mkdir -p models
mkdir -p result

echo_success "构建目录准备完成: $BUILD_DIR"

# 编译项目
echo_step "7. 编译项目"

echo_info "正在生成Makefile..."
"$QT_BIN_DIR/qmake" ../ModelConverter.pro

if [ $? -ne 0 ]; then
    echo_error "qmake执行失败"
    exit 1
fi

echo_success "Makefile生成成功"

echo_info "正在编译项目..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo_error "项目编译失败"
    exit 1
fi

echo_success "项目编译成功"

# 复制必要的库文件
echo_step "8. 准备运行环境"

if [ ! -f "libsf-core-ls.so" ]; then
    echo_info "复制动态库文件..."
    cp ../libsf-core-ls.so .
fi

# 设置库路径
export LD_LIBRARY_PATH=".:$LD_LIBRARY_PATH"

echo_success "运行环境准备完成"

# 检查可执行文件
EXECUTABLE="ModelConverter"
if [ ! -f "$EXECUTABLE" ]; then
    echo_error "可执行文件不存在: $EXECUTABLE"
    exit 1
fi

# 检查依赖
echo_info "检查程序依赖..."
ldd ./"$EXECUTABLE" | grep "not found" && echo_warning "发现缺失的依赖库" || echo_success "所有依赖库都可用"

# 显示激活信息
echo_step "9. 激活信息"
echo_info ""
echo_info "=== 激活模式说明 ==="
echo_info "1. 生产模式（默认）:"
echo_info "   - 需要有效的激活码"
echo_info "   - 硬件绑定验证"
echo_info "   - 在线激活验证"
echo_info ""
echo_info "2. 调试模式:"
echo_info "   - 启用调试模式复选框"
echo_info "   - 可使用测试激活码"
echo_info "   - 离线验证"
echo_info ""
echo_info "=== 测试激活码（调试模式）==="
echo_info "  TEST-1234-5678-ABCD"
echo_info "  DEBUG-2024-0629-DEMO"
echo_info "  DEV-AUTOBIT-MODEL-CONV"
echo_info "  E76G-JEQR-EQRA-T7ZW"
echo_info ""
echo_info "=== 功能特性 ==="
echo_info "✅ 现代化UI设计"
echo_info "✅ 双模式激活系统"
echo_info "✅ 硬件指纹绑定"
echo_info "✅ 加密许可证存储"
echo_info "✅ YOLOv5/v8模型转换"
echo_info "✅ Docker集成转换"
echo_info ""

# 启动应用程序
echo_step "10. 启动应用程序"
echo_info "正在启动AutoBit模型转换工具..."
echo_info ""

./"$EXECUTABLE"

echo_success "程序已退出"
