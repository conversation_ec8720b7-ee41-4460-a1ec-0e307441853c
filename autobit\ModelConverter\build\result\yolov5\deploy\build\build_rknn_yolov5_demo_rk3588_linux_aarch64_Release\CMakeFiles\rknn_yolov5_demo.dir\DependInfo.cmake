# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/workspace/yolo-rk3588/yolov5/cpp/main.cc" "/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles/rknn_yolov5_demo.dir/main.cc.o"
  "/workspace/yolo-rk3588/yolov5/cpp/postprocess.cc" "/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles/rknn_yolov5_demo.dir/postprocess.cc.o"
  "/workspace/yolo-rk3588/yolov5/cpp/rknpu2/yolov5.cc" "/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles/rknn_yolov5_demo.dir/rknpu2/yolov5.cc.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "/workspace/yolo-rk3588/yolov5/cpp"
  "/workspace/yolo-rk3588/3rdparty/rknpu2/include"
  "/workspace/yolo-rk3588/utils"
  "/workspace/yolo-rk3588/3rdparty/stb_image"
  "/workspace/yolo-rk3588/3rdparty/librga/include"
  "/workspace/yolo-rk3588/3rdparty/jpeg_turbo/include"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out/CMakeFiles/imageutils.dir/DependInfo.cmake"
  "/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out/CMakeFiles/fileutils.dir/DependInfo.cmake"
  "/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out/CMakeFiles/imagedrawing.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
