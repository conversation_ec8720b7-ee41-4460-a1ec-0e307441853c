#!/bin/bash
# 药盒识别与用药建议应用启动脚本
# 集成双模式激活验证功能

# 设置工作目录
cd "$(dirname "${BASH_SOURCE[0]}")"

# 设置Qt环境变量
export LD_LIBRARY_PATH="/usr/lib/aarch64-linux-gnu/qt5/lib:${LD_LIBRARY_PATH}"
export QT_PLUGIN_PATH="/usr/lib/aarch64-linux-gnu/qt5/plugins"
export LD_LIBRARY_PATH="/userdata/demo_Linux_aarch64/lib:${LD_LIBRARY_PATH}"

# 设置字体环境变量
export QT_QPA_FONTDIR="/usr/share/fonts"
export FONTCONFIG_PATH="/etc/fonts"

# 启动应用程序
echo "=== 药盒识别与用药建议系统 ==="
echo "目标平台: RK3588 (ARM64)"
echo "激活功能: 双模式验证系统"
echo ""
echo "激活说明:"
echo "1. 生产模式: 需要有效激活码，硬件绑定"
echo "2. 调试模式: 可使用测试激活码"
echo "   - TEST-1234-5678-ABCD"
echo "   - RK3588-MEDICAL-TEST"
echo "   - DEV-DIAGNOSIS-APP-RK"
echo ""
echo "启动应用程序..."
./DiagnosisApp
