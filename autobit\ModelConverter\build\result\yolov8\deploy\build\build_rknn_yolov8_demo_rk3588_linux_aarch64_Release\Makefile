# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspace/yolo-rk3588/yolov8/cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named rknn_yolov8_demo_zero_copy

# Build rule for target.
rknn_yolov8_demo_zero_copy: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 rknn_yolov8_demo_zero_copy
.PHONY : rknn_yolov8_demo_zero_copy

# fast build rule for target.
rknn_yolov8_demo_zero_copy/fast:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build
.PHONY : rknn_yolov8_demo_zero_copy/fast

#=============================================================================
# Target rules for targets named rknn_yolov8_demo

# Build rule for target.
rknn_yolov8_demo: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 rknn_yolov8_demo
.PHONY : rknn_yolov8_demo

# fast build rule for target.
rknn_yolov8_demo/fast:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/build
.PHONY : rknn_yolov8_demo/fast

#=============================================================================
# Target rules for targets named audioutils

# Build rule for target.
audioutils: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 audioutils
.PHONY : audioutils

# fast build rule for target.
audioutils/fast:
	$(MAKE) -f utils.out/CMakeFiles/audioutils.dir/build.make utils.out/CMakeFiles/audioutils.dir/build
.PHONY : audioutils/fast

#=============================================================================
# Target rules for targets named imageutils

# Build rule for target.
imageutils: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 imageutils
.PHONY : imageutils

# fast build rule for target.
imageutils/fast:
	$(MAKE) -f utils.out/CMakeFiles/imageutils.dir/build.make utils.out/CMakeFiles/imageutils.dir/build
.PHONY : imageutils/fast

#=============================================================================
# Target rules for targets named imagedrawing

# Build rule for target.
imagedrawing: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 imagedrawing
.PHONY : imagedrawing

# fast build rule for target.
imagedrawing/fast:
	$(MAKE) -f utils.out/CMakeFiles/imagedrawing.dir/build.make utils.out/CMakeFiles/imagedrawing.dir/build
.PHONY : imagedrawing/fast

#=============================================================================
# Target rules for targets named fileutils

# Build rule for target.
fileutils: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 fileutils
.PHONY : fileutils

# fast build rule for target.
fileutils/fast:
	$(MAKE) -f utils.out/CMakeFiles/fileutils.dir/build.make utils.out/CMakeFiles/fileutils.dir/build
.PHONY : fileutils/fast

main.o: main.cc.o

.PHONY : main.o

# target to build an object file
main.cc.o:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/main.cc.o
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/main.cc.o
.PHONY : main.cc.o

main.i: main.cc.i

.PHONY : main.i

# target to preprocess a source file
main.cc.i:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/main.cc.i
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/main.cc.i
.PHONY : main.cc.i

main.s: main.cc.s

.PHONY : main.s

# target to generate assembly for a file
main.cc.s:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/main.cc.s
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/main.cc.s
.PHONY : main.cc.s

postprocess.o: postprocess.cc.o

.PHONY : postprocess.o

# target to build an object file
postprocess.cc.o:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/postprocess.cc.o
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.o
.PHONY : postprocess.cc.o

postprocess.i: postprocess.cc.i

.PHONY : postprocess.i

# target to preprocess a source file
postprocess.cc.i:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/postprocess.cc.i
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.i
.PHONY : postprocess.cc.i

postprocess.s: postprocess.cc.s

.PHONY : postprocess.s

# target to generate assembly for a file
postprocess.cc.s:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/postprocess.cc.s
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.s
.PHONY : postprocess.cc.s

rknpu2/yolov8.o: rknpu2/yolov8.cc.o

.PHONY : rknpu2/yolov8.o

# target to build an object file
rknpu2/yolov8.cc.o:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.o
.PHONY : rknpu2/yolov8.cc.o

rknpu2/yolov8.i: rknpu2/yolov8.cc.i

.PHONY : rknpu2/yolov8.i

# target to preprocess a source file
rknpu2/yolov8.cc.i:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.i
.PHONY : rknpu2/yolov8.cc.i

rknpu2/yolov8.s: rknpu2/yolov8.cc.s

.PHONY : rknpu2/yolov8.s

# target to generate assembly for a file
rknpu2/yolov8.cc.s:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo.dir/build.make CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.s
.PHONY : rknpu2/yolov8.cc.s

rknpu2/yolov8_zero_copy.o: rknpu2/yolov8_zero_copy.cc.o

.PHONY : rknpu2/yolov8_zero_copy.o

# target to build an object file
rknpu2/yolov8_zero_copy.cc.o:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/rknpu2/yolov8_zero_copy.cc.o
.PHONY : rknpu2/yolov8_zero_copy.cc.o

rknpu2/yolov8_zero_copy.i: rknpu2/yolov8_zero_copy.cc.i

.PHONY : rknpu2/yolov8_zero_copy.i

# target to preprocess a source file
rknpu2/yolov8_zero_copy.cc.i:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/rknpu2/yolov8_zero_copy.cc.i
.PHONY : rknpu2/yolov8_zero_copy.cc.i

rknpu2/yolov8_zero_copy.s: rknpu2/yolov8_zero_copy.cc.s

.PHONY : rknpu2/yolov8_zero_copy.s

# target to generate assembly for a file
rknpu2/yolov8_zero_copy.cc.s:
	$(MAKE) -f CMakeFiles/rknn_yolov8_demo_zero_copy.dir/build.make CMakeFiles/rknn_yolov8_demo_zero_copy.dir/rknpu2/yolov8_zero_copy.cc.s
.PHONY : rknpu2/yolov8_zero_copy.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... rknn_yolov8_demo_zero_copy"
	@echo "... rknn_yolov8_demo"
	@echo "... audioutils"
	@echo "... imageutils"
	@echo "... imagedrawing"
	@echo "... fileutils"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... postprocess.o"
	@echo "... postprocess.i"
	@echo "... postprocess.s"
	@echo "... rknpu2/yolov8.o"
	@echo "... rknpu2/yolov8.i"
	@echo "... rknpu2/yolov8.s"
	@echo "... rknpu2/yolov8_zero_copy.o"
	@echo "... rknpu2/yolov8_zero_copy.i"
	@echo "... rknpu2/yolov8_zero_copy.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

