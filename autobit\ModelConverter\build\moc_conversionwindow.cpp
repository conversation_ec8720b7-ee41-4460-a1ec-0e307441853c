/****************************************************************************
** Meta object code from reading C++ file 'conversionwindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.6)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../conversionwindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'conversionwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.6. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ConversionWindow_t {
    QByteArrayData data[10];
    char stringdata0[153];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ConversionWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ConversionWindow_t qt_meta_stringdata_ConversionWindow = {
    {
QT_MOC_LITERAL(0, 0, 16), // "ConversionWindow"
QT_MOC_LITERAL(1, 17, 15), // "onBrowseClicked"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 16), // "onConvertClicked"
QT_MOC_LITERAL(4, 51, 15), // "onProcessOutput"
QT_MOC_LITERAL(5, 67, 20), // "onProcessErrorOutput"
QT_MOC_LITERAL(6, 88, 17), // "onProcessFinished"
QT_MOC_LITERAL(7, 106, 8), // "exitCode"
QT_MOC_LITERAL(8, 115, 17), // "onClearLogClicked"
QT_MOC_LITERAL(9, 133, 19) // "onOpenResultClicked"

    },
    "ConversionWindow\0onBrowseClicked\0\0"
    "onConvertClicked\0onProcessOutput\0"
    "onProcessErrorOutput\0onProcessFinished\0"
    "exitCode\0onClearLogClicked\0"
    "onOpenResultClicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ConversionWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   49,    2, 0x08 /* Private */,
       3,    0,   50,    2, 0x08 /* Private */,
       4,    0,   51,    2, 0x08 /* Private */,
       5,    0,   52,    2, 0x08 /* Private */,
       6,    1,   53,    2, 0x08 /* Private */,
       8,    0,   56,    2, 0x08 /* Private */,
       9,    0,   57,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    7,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ConversionWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConversionWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onBrowseClicked(); break;
        case 1: _t->onConvertClicked(); break;
        case 2: _t->onProcessOutput(); break;
        case 3: _t->onProcessErrorOutput(); break;
        case 4: _t->onProcessFinished((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 5: _t->onClearLogClicked(); break;
        case 6: _t->onOpenResultClicked(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ConversionWindow::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_ConversionWindow.data,
    qt_meta_data_ConversionWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ConversionWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ConversionWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ConversionWindow.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ConversionWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
