#!/bin/bash
# YOLO模型转换脚本 - Linux主机环境
# 基于autobit_back项目，支持YOLOv5和YOLOv8模型转换
# 包含完整的转换、压缩、编译和部署包生成流程

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }
echo_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }
echo_cmd() { echo -e "${CYAN}[CMD]${NC} $1"; }

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${SCRIPT_DIR}"
AUTOBIT_BACK_DIR="${PROJECT_ROOT}/autobit_back"
YOLO_RK3588_DIR="${AUTOBIT_BACK_DIR}/yolo-rk3588"

# 默认配置
DOCKER_IMAGE_NAME="model-converter:latest"
TARGET_SOC="rk3588"
TARGET_ARCH="aarch64"
QUANTIZATION="i8"

# 工作目录
WORK_DIR="${PROJECT_ROOT}/workspace"
MODELS_DIR="${WORK_DIR}/models"
RESULT_DIR="${WORK_DIR}/result"
LOGS_DIR="${WORK_DIR}/logs"

# 参数变量
MODEL_TYPE=""
MODEL_WEIGHT=""
OUTPUT_DIR=""
VERBOSE=false

# 显示帮助信息
show_help() {
    cat << EOF
YOLO模型转换工具 - Linux主机环境

用法: $0 [选项]

必需参数:
  --model MODEL_TYPE     模型类型 (yolov5 或 yolov8)
  --weight WEIGHT_PATH   模型权重文件路径 (.pt格式)

可选参数:
  --output OUTPUT_DIR    输出目录 (默认: ${RESULT_DIR})
  --soc TARGET_SOC       目标SoC (默认: rk3588)
  --arch TARGET_ARCH     目标架构 (默认: aarch64)
  --quant QUANTIZATION   量化类型 (默认: i8)
  --verbose              详细输出
  --help                 显示此帮助信息

示例:
  $0 --model yolov8 --weight /path/to/yolov8n.pt
  $0 --model yolov5 --weight /path/to/yolov5s.pt --output /custom/output
  $0 --model yolov8 --weight models/yolov8n.pt --verbose

支持的模型类型:
  - yolov5: YOLOv5系列模型
  - yolov8: YOLOv8系列模型

输出文件:
  - ONNX模型: {model_name}.onnx
  - RKNN模型: {model_name}.rknn
  - 部署包: deploy/install/rk3588_linux_aarch64/
  - 日志文件: 转换过程的详细日志

EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --model)
                MODEL_TYPE="$2"
                shift 2
                ;;
            --weight)
                MODEL_WEIGHT="$2"
                shift 2
                ;;
            --output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            --soc)
                TARGET_SOC="$2"
                shift 2
                ;;
            --arch)
                TARGET_ARCH="$2"
                shift 2
                ;;
            --quant)
                QUANTIZATION="$2"
                shift 2
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                echo_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证参数
validate_arguments() {
    echo_step "验证输入参数..."
    
    # 检查必需参数
    if [[ -z "${MODEL_TYPE}" ]]; then
        echo_error "缺少必需参数: --model"
        show_help
        exit 1
    fi
    
    if [[ -z "${MODEL_WEIGHT}" ]]; then
        echo_error "缺少必需参数: --weight"
        show_help
        exit 1
    fi
    
    # 验证模型类型
    if [[ "${MODEL_TYPE}" != "yolov5" && "${MODEL_TYPE}" != "yolov8" ]]; then
        echo_error "不支持的模型类型: ${MODEL_TYPE}"
        echo_info "支持的类型: yolov5, yolov8"
        exit 1
    fi
    
    # 检查模型文件
    if [[ ! -f "${MODEL_WEIGHT}" ]]; then
        echo_error "模型文件不存在: ${MODEL_WEIGHT}"
        exit 1
    fi
    
    # 检查文件扩展名
    if [[ "${MODEL_WEIGHT}" != *.pt ]]; then
        echo_warning "⚠️ 模型文件不是.pt格式: ${MODEL_WEIGHT}"
    fi
    
    # 设置默认输出目录
    if [[ -z "${OUTPUT_DIR}" ]]; then
        OUTPUT_DIR="${RESULT_DIR}"
    fi
    
    echo_success "✓ 参数验证通过"
    echo_info "模型类型: ${MODEL_TYPE}"
    echo_info "模型文件: ${MODEL_WEIGHT}"
    echo_info "输出目录: ${OUTPUT_DIR}"
    echo_info "目标平台: ${TARGET_SOC}_${TARGET_ARCH}"
    echo_info "量化类型: ${QUANTIZATION}"
}

# 准备工作环境
prepare_environment() {
    echo_step "准备工作环境..."
    
    # 创建必要目录
    mkdir -p "${OUTPUT_DIR}"
    mkdir -p "${MODELS_DIR}"
    mkdir -p "${LOGS_DIR}"
    
    # 复制模型文件到工作目录
    MODEL_FILENAME=$(basename "${MODEL_WEIGHT}")
    WORK_MODEL_PATH="${MODELS_DIR}/${MODEL_FILENAME}"
    
    if [[ "${MODEL_WEIGHT}" != "${WORK_MODEL_PATH}" ]]; then
        echo_info "复制模型文件到工作目录..."
        cp "${MODEL_WEIGHT}" "${WORK_MODEL_PATH}"
        echo_success "✓ 模型文件复制完成"
    fi
    
    # 检查Docker镜像
    if ! docker images | grep -q "model-converter"; then
        echo_error "Docker镜像未找到，请先运行部署脚本"
        echo_cmd "./linux_host_deploy.sh"
        exit 1
    fi
    
    echo_success "工作环境准备完成"
}

# 执行模型转换
execute_conversion() {
    echo_step "执行模型转换..."
    
    # 设置Docker运行参数
    DOCKER_VOLUMES="-v ${MODELS_DIR}:/workspace/yolo-rk3588/models"
    DOCKER_VOLUMES="${DOCKER_VOLUMES} -v ${OUTPUT_DIR}:/workspace/yolo-rk3588/result"
    DOCKER_VOLUMES="${DOCKER_VOLUMES} -v ${LOGS_DIR}:/workspace/yolo-rk3588/logs"
    
    # 设置转换参数
    CONVERT_ARGS="--model ${MODEL_TYPE}"
    CONVERT_ARGS="${CONVERT_ARGS} --weight /workspace/yolo-rk3588/models/${MODEL_FILENAME}"
    CONVERT_ARGS="${CONVERT_ARGS} --output result"
    
    # 构建Docker命令
    DOCKER_CMD="docker run --rm ${DOCKER_VOLUMES}"
    DOCKER_CMD="${DOCKER_CMD} -w /workspace/yolo-rk3588"
    DOCKER_CMD="${DOCKER_CMD} ${DOCKER_IMAGE_NAME}"
    DOCKER_CMD="${DOCKER_CMD} ${CONVERT_ARGS}"
    
    echo_info "执行Docker转换命令..."
    if [[ "${VERBOSE}" == "true" ]]; then
        echo_cmd "${DOCKER_CMD}"
    fi
    
    # 记录开始时间
    START_TIME=$(date +%s)
    
    # 执行转换
    if [[ "${VERBOSE}" == "true" ]]; then
        eval "${DOCKER_CMD}"
    else
        eval "${DOCKER_CMD}" > "${LOGS_DIR}/conversion_$(date +%Y%m%d_%H%M%S).log" 2>&1
    fi
    
    # 记录结束时间
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    echo_success "✓ 模型转换完成，耗时: ${DURATION}秒"
}

# 验证转换结果
verify_results() {
    echo_step "验证转换结果..."
    
    # 检查输出目录结构
    MODEL_OUTPUT_DIR="${OUTPUT_DIR}/${MODEL_TYPE}"
    
    if [[ ! -d "${MODEL_OUTPUT_DIR}" ]]; then
        echo_error "转换失败：未找到输出目录 ${MODEL_OUTPUT_DIR}"
        exit 1
    fi
    
    # 检查ONNX模型
    ONNX_FILE="${MODEL_OUTPUT_DIR}/${MODEL_TYPE}.onnx"
    if [[ -f "${ONNX_FILE}" ]]; then
        ONNX_SIZE=$(du -h "${ONNX_FILE}" | cut -f1)
        echo_success "✓ ONNX模型: ${ONNX_FILE} (${ONNX_SIZE})"
    else
        echo_warning "⚠️ 未找到ONNX模型文件"
    fi
    
    # 检查RKNN模型
    RKNN_FILE="${MODEL_OUTPUT_DIR}/${MODEL_TYPE}.rknn"
    if [[ -f "${RKNN_FILE}" ]]; then
        RKNN_SIZE=$(du -h "${RKNN_FILE}" | cut -f1)
        echo_success "✓ RKNN模型: ${RKNN_FILE} (${RKNN_SIZE})"
    else
        echo_error "转换失败：未找到RKNN模型文件"
        exit 1
    fi
    
    # 检查部署包
    DEPLOY_DIR="${MODEL_OUTPUT_DIR}/deploy/install/${TARGET_SOC}_linux_${TARGET_ARCH}"
    if [[ -d "${DEPLOY_DIR}" ]]; then
        echo_success "✓ 部署包: ${DEPLOY_DIR}"
        
        # 列出部署包内容
        if [[ "${VERBOSE}" == "true" ]]; then
            echo_info "部署包内容:"
            ls -la "${DEPLOY_DIR}"
        fi
        
        # 检查可执行文件
        DEMO_EXECUTABLE="${DEPLOY_DIR}/rknn_${MODEL_TYPE}_demo"
        if [[ -f "${DEMO_EXECUTABLE}" ]]; then
            echo_success "✓ 可执行文件: ${DEMO_EXECUTABLE}"
        else
            echo_warning "⚠️ 未找到可执行文件"
        fi
    else
        echo_warning "⚠️ 未找到部署包目录"
    fi
    
    echo_success "结果验证完成"
}

# 生成部署包
generate_deployment_package() {
    echo_step "生成部署包..."
    
    DEPLOY_DIR="${OUTPUT_DIR}/${MODEL_TYPE}/deploy/install/${TARGET_SOC}_linux_${TARGET_ARCH}"
    
    if [[ -d "${DEPLOY_DIR}" ]]; then
        # 创建压缩包
        PACKAGE_NAME="${MODEL_TYPE}_${TARGET_SOC}_deploy_$(date +%Y%m%d_%H%M%S).tar.gz"
        PACKAGE_PATH="${OUTPUT_DIR}/${PACKAGE_NAME}"
        
        echo_info "创建部署压缩包..."
        cd "${DEPLOY_DIR}"
        tar -czf "${PACKAGE_PATH}" *
        cd - > /dev/null
        
        PACKAGE_SIZE=$(du -h "${PACKAGE_PATH}" | cut -f1)
        echo_success "✓ 部署包已创建: ${PACKAGE_PATH} (${PACKAGE_SIZE})"
        
        # 生成部署说明
        DEPLOY_README="${OUTPUT_DIR}/DEPLOY_README.txt"
        cat > "${DEPLOY_README}" << EOF
YOLO模型部署包使用说明
========================

模型信息:
- 模型类型: ${MODEL_TYPE}
- 目标平台: ${TARGET_SOC}_${TARGET_ARCH}
- 量化类型: ${QUANTIZATION}
- 生成时间: $(date)

部署步骤:
1. 将部署包传输到目标设备:
   scp ${PACKAGE_NAME} user@device_ip:/home/<USER>/

2. 在目标设备上解压:
   cd /home/<USER>/
   tar -xzf ${PACKAGE_NAME}

3. 设置运行环境:
   export LD_LIBRARY_PATH=./lib:\$LD_LIBRARY_PATH

4. 运行推理:
   ./rknn_${MODEL_TYPE}_demo model/${MODEL_TYPE}.rknn input_image.jpg

文件说明:
- rknn_${MODEL_TYPE}_demo: 可执行文件
- model/: 模型文件目录
- lib/: 依赖库目录
- include/: 头文件目录

EOF
        
        echo_success "✓ 部署说明已生成: ${DEPLOY_README}"
    else
        echo_warning "⚠️ 跳过部署包生成（未找到部署目录）"
    fi
}

# 主函数
main() {
    echo_step "=== YOLO模型转换工具 - Linux主机环境 ==="
    
    # 解析和验证参数
    parse_arguments "$@"
    validate_arguments
    
    # 执行转换流程
    prepare_environment
    execute_conversion
    verify_results
    generate_deployment_package
    
    echo_step "=== 转换完成 ==="
    echo_success "🎉 YOLO模型转换成功！"
    
    echo_info "=== 输出文件 ==="
    echo_info "ONNX模型: ${OUTPUT_DIR}/${MODEL_TYPE}/${MODEL_TYPE}.onnx"
    echo_info "RKNN模型: ${OUTPUT_DIR}/${MODEL_TYPE}/${MODEL_TYPE}.rknn"
    echo_info "部署目录: ${OUTPUT_DIR}/${MODEL_TYPE}/deploy/"
    echo_info "部署包: ${OUTPUT_DIR}/${MODEL_TYPE}_${TARGET_SOC}_deploy_*.tar.gz"
    echo_info "说明文档: ${OUTPUT_DIR}/DEPLOY_README.txt"
    
    echo_success "转换脚本执行完成！"
}

# 执行主函数
main "$@"
