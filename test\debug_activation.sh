#!/bin/bash
# 激活系统调试脚本
# 用于RK3588设备上的激活功能验证

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo_info "=== 激活系统调试脚本 ==="
echo_info "适用于RK3588边缘设备"
echo_info ""

# 检查系统信息
echo_info "1. 系统信息检查"
echo "系统架构: $(uname -m)"
echo "内核版本: $(uname -r)"
echo "系统版本: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"')"
echo ""

# 检查硬件信息
echo_info "2. 硬件信息检查"

echo "CPU信息:"
if [ -f /proc/cpuinfo ]; then
    echo "  型号: $(cat /proc/cpuinfo | grep 'model name' | head -1 | cut -d':' -f2 | xargs)"
    echo "  硬件: $(cat /proc/cpuinfo | grep 'Hardware' | head -1 | cut -d':' -f2 | xargs)"
    echo "  核心数: $(cat /proc/cpuinfo | grep 'processor' | wc -l)"
else
    echo_warning "  无法读取CPU信息"
fi

echo ""
echo "网络接口:"
if command -v ip &> /dev/null; then
    ip link show | grep -E '^[0-9]+:' | while read line; do
        interface=$(echo $line | cut -d':' -f2 | xargs)
        mac=$(echo $line | grep -o 'link/ether [^ ]*' | cut -d' ' -f2)
        if [ ! -z "$mac" ] && [ "$mac" != "00:00:00:00:00:00" ]; then
            echo "  $interface: $mac"
        fi
    done
else
    echo_warning "  无法获取网络接口信息"
fi

echo ""
echo "存储设备:"
if command -v lsblk &> /dev/null; then
    lsblk -o NAME,SIZE,TYPE,MOUNTPOINT | head -10
else
    echo_warning "  无法获取存储设备信息"
fi

echo ""

# 检查设备树信息（RK3588特有）
echo_info "3. RK3588设备信息"
if [ -f /proc/device-tree/model ]; then
    echo "设备型号: $(cat /proc/device-tree/model)"
fi

if [ -f /proc/device-tree/compatible ]; then
    echo "兼容性: $(cat /proc/device-tree/compatible | tr '\0' ' ')"
fi

echo ""

# 检查Qt环境
echo_info "4. Qt环境检查"
if command -v qmake &> /dev/null; then
    echo "Qt版本: $(qmake --version | grep Qt)"
else
    echo_warning "qmake未找到"
fi

# 检查Qt库
QT_LIB_PATH="/usr/lib/aarch64-linux-gnu/qt5/lib"
if [ -d "$QT_LIB_PATH" ]; then
    echo_success "Qt库路径存在: $QT_LIB_PATH"
    echo "Qt库文件数量: $(ls $QT_LIB_PATH/libQt5*.so* 2>/dev/null | wc -l)"
else
    echo_warning "Qt库路径不存在: $QT_LIB_PATH"
fi

echo ""

# 检查应用程序
echo_info "5. 应用程序检查"
if [ -f "build/DiagnosisApp" ]; then
    echo_success "应用程序存在: build/DiagnosisApp"
    
    # 检查依赖
    echo "检查应用程序依赖:"
    if command -v ldd &> /dev/null; then
        missing_deps=$(ldd build/DiagnosisApp | grep "not found" | wc -l)
        if [ $missing_deps -eq 0 ]; then
            echo_success "所有依赖库都可用"
        else
            echo_warning "发现 $missing_deps 个缺失的依赖库"
            ldd build/DiagnosisApp | grep "not found"
        fi
    else
        echo_warning "ldd命令不可用，无法检查依赖"
    fi
else
    echo_error "应用程序不存在，请先运行 ./build.sh 进行编译"
fi

echo ""

# 检查许可证配置目录
echo_info "6. 许可证系统检查"
CONFIG_DIR="$HOME/.config/DiagnosisApp"
if [ -d "$CONFIG_DIR" ]; then
    echo "许可证配置目录: $CONFIG_DIR"
    if [ -f "$CONFIG_DIR/diagnosis_license.conf" ]; then
        echo_warning "发现现有许可证文件"
        echo "建议清除测试: rm -f $CONFIG_DIR/diagnosis_license.conf"
    else
        echo "许可证文件: 不存在（首次运行）"
    fi
else
    echo "许可证配置目录: 不存在（将在首次运行时创建）"
fi

echo ""

# 激活码格式测试
echo_info "7. 激活码格式测试"
TEST_CODES=(
    "TEST-1234-5678-ABCD"      # 有效格式
    "RK3588-MEDICAL-TEST"      # 有效格式
    "DEV-DIAGNOSIS-APP-RK"     # 有效格式
    "invalid-code"             # 无效格式
    "1234-5678-9012"           # 无效格式（长度不够）
)

for code in "${TEST_CODES[@]}"; do
    if [[ $code =~ ^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$ ]]; then
        echo_success "✓ $code - 格式有效"
    else
        echo_warning "✗ $code - 格式无效"
    fi
done

echo ""

# 网络连接测试
echo_info "8. 网络连接测试"
if ping -c 1 -W 3 8.8.8.8 &> /dev/null; then
    echo_success "网络连接正常"
    
    # 测试激活服务器连接
    if command -v curl &> /dev/null; then
        echo "测试激活服务器连接..."
        if curl -s --connect-timeout 5 https://api.shifang.co &> /dev/null; then
            echo_success "激活服务器可达"
        else
            echo_warning "激活服务器不可达（生产模式可能无法使用）"
        fi
    else
        echo_warning "curl命令不可用，无法测试服务器连接"
    fi
else
    echo_warning "网络连接异常（调试模式仍可使用）"
fi

echo ""

# 显示使用说明
echo_info "9. 使用说明"
echo "编译应用程序:"
echo "  ./build.sh"
echo ""
echo "启动应用程序:"
echo "  cd build && ./run_diagnosis_app.sh"
echo ""
echo "调试模式激活码:"
echo "  TEST-1234-5678-ABCD"
echo "  RK3588-MEDICAL-TEST"
echo "  DEV-DIAGNOSIS-APP-RK"
echo ""
echo "清除许可证（用于测试）:"
echo "  rm -f ~/.config/DiagnosisApp/diagnosis_license.conf"
echo ""

echo_success "调试脚本执行完成！"
