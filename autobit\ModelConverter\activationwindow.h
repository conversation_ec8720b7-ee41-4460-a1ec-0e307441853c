#ifndef ACTIVATIONWINDOW_H
#define ACTIVATIONWINDOW_H

#include <QWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFrame>
#include <QGraphicsDropShadowEffect>
#include <QPropertyAnimation>
#include <QTimer>
#include <QComboBox>
#include <QCheckBox>
#include <QGroupBox>
#include <QProgressBar>
#include <QMovie>
#include "conversionwindow.h"
#include "LicenseManager.h"

class ActivationWindow : public QWidget
{
    Q_OBJECT
public:
    explicit ActivationWindow(QWidget *parent = nullptr);

private slots:
    void onActivateClicked();
    void onDebugModeChanged(bool enabled);
    void showDeviceInfo();
    void onActivationFinished(LicenseManager::ActivationResult result, const QString &message);
    void onLicenseStatusChanged(bool isValid);

private:
    void setupUI();
    void setupStyles();
    void setupAnimations();
    void setStatus(const QString &message, const QString &type = "info");
    void showActivationSuccess();
    void showActivationError(const QString &error);

    // 激活逻辑
    QString getCurrentDeviceId();

    // UI组件
    QFrame *mainFrame;
    QLabel *titleLabel;
    QLabel *logoLabel;
    QLineEdit *licenseEdit;
    QPushButton *activateButton;
    QLabel *statusLabel;
    QLabel *deviceInfoLabel;
    QPushButton *deviceInfoButton;
    QCheckBox *debugModeCheckBox;
    QComboBox *debugDeviceCombo;
    QProgressBar *progressBar;
    QLabel *loadingLabel;

    // 动画
    QPropertyAnimation *statusAnimation;
    QPropertyAnimation *buttonAnimation;
    QTimer *statusTimer;
    QMovie *loadingMovie;

    // 窗口
    ConversionWindow *convWindow;

    // 许可证管理器
    LicenseManager *licenseManager;

    // 状态
    bool isDebugMode;
    bool isActivating;
};

#endif