===================================
BUILD_DEMO_NAME:   yolov8
BUILD_DEMO_PATH:   /workspace/yolo-rk3588/yolov8/cpp
TARGET_SOC:        rk3588
TARGET_ARCH:       aarch64
BUILD_TYPE:        Release
ENABLE_ASAN:       OFF
DISABLE_RGA:       OFF
DISABLE_LIBJPEG:   OFF
CC:                /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
CXX:               /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++
INPUT_PATH:        /workspace/yolo-rk3588/result/yolov8
OUTPUT_PATH:       /workspace/yolo-rk3588/result/yolov8/deploy
INSTALL_DIR:       /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo
BUILD_DIR:         /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release
===================================
-- The C compiler identification is GNU 6.3.1
-- The CXX compiler identification is GNU 6.3.1
-- Check for working C compiler: /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
-- Check for working C compiler: /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -- works
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Detecting C compile features
-- Detecting C compile features - done
-- Check for working CXX compiler: /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++
-- Check for working CXX compiler: /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ -- works
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- !!!!!!!!!!!CMAKE_SYSTEM_NAME: Linux
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed
-- Check if compiler accepts -pthread
-- Check if compiler accepts -pthread - yes
-- Found Threads: TRUE  
-- Configuring done
-- Generating done
CMake Warning:
  Manually-specified variables were not used by the project:

    OpenCV_DIR


-- Build files have been written to: /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release
make: Warning: File 'Makefile' has modification time 1.5 s in the future
make[1]: Warning: File 'CMakeFiles/Makefile2' has modification time 2.1 s in the future
make[2]: Warning: File 'utils.out/CMakeFiles/imageutils.dir/flags.make' has modification time 1.6 s in the future
make[2]: Warning: File 'utils.out/CMakeFiles/audioutils.dir/flags.make' has modification time 1.6 s in the future
make[2]: Warning: File 'utils.out/CMakeFiles/imagedrawing.dir/flags.make' has modification time 1.7 s in the future
make[2]: Warning: File 'utils.out/CMakeFiles/fileutils.dir/flags.make' has modification time 1.8 s in the future
Scanning dependencies of target fileutils
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
Scanning dependencies of target imagedrawing
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
Scanning dependencies of target audioutils
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
make[2]: Warning: File 'utils.out/CMakeFiles/fileutils.dir/flags.make' has modification time 1.7 s in the future
make[2]: Warning: File 'utils.out/CMakeFiles/imagedrawing.dir/flags.make' has modification time 1.6 s in the future
make[2]: Warning: File 'utils.out/CMakeFiles/audioutils.dir/flags.make' has modification time 1.5 s in the future
Scanning dependencies of target imageutils
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
[ 12%] Building C object utils.out/CMakeFiles/fileutils.dir/file_utils.c.o
make[2]: Warning: File 'utils.out/CMakeFiles/imageutils.dir/flags.make' has modification time 1.5 s in the future
[ 12%] Building C object utils.out/CMakeFiles/imagedrawing.dir/image_drawing.c.o
[ 18%] Building C object utils.out/CMakeFiles/audioutils.dir/audio_utils.c.o
[ 25%] Building C object utils.out/CMakeFiles/imageutils.dir/image_utils.c.o
/workspace/yolo-rk3588/utils/image_drawing.c: In function 'rbbox_to_corners':
/workspace/yolo-rk3588/utils/image_drawing.c:1541:19: warning: implicit declaration of function 'cos' [-Wimplicit-function-declaration]
     float a_cos = cos(angle);
                   ^~~
/workspace/yolo-rk3588/utils/image_drawing.c:1541:19: warning: incompatible implicit declaration of built-in function 'cos'
/workspace/yolo-rk3588/utils/image_drawing.c:1541:19: note: include '<math.h>' or provide a declaration of 'cos'
/workspace/yolo-rk3588/utils/image_drawing.c:1542:19: warning: implicit declaration of function 'sin' [-Wimplicit-function-declaration]
     float a_sin = sin(angle);
                   ^~~
/workspace/yolo-rk3588/utils/image_drawing.c:1542:19: warning: incompatible implicit declaration of built-in function 'sin'
/workspace/yolo-rk3588/utils/image_drawing.c:1542:19: note: include '<math.h>' or provide a declaration of 'sin'
/workspace/yolo-rk3588/utils/image_utils.c: In function 'write_image':
/workspace/yolo-rk3588/utils/image_utils.c:290:35: warning: passing argument 1 of 'get_image_size' discards 'const' qualifier from pointer target type [-Wdiscarded-qualifiers]
         int size = get_image_size(img);
                                   ^~~
In file included from /workspace/yolo-rk3588/utils/image_utils.c:19:0:
/workspace/yolo-rk3588/utils/image_utils.h:67:5: note: expected 'image_buffer_t * {aka struct <anonymous> *}' but argument is of type 'const image_buffer_t * {aka const struct <anonymous> *}'
 int get_image_size(image_buffer_t* image);
     ^~~~~~~~~~~~~~
/workspace/yolo-rk3588/utils/image_utils.c: In function 'convert_image_rga':
/workspace/yolo-rk3588/utils/image_utils.c:634:27: warning: initialization from incompatible pointer type [-Wincompatible-pointer-types]
         char* p_imcolor = &imcolor;
                           ^
[ 37%] Linking C static library libfileutils.a
[ 37%] Linking C static library libaudioutils.a
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
[ 37%] Built target fileutils
[ 37%] Built target audioutils
[ 43%] Linking C static library libimagedrawing.a
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
[ 43%] Built target imagedrawing
[ 50%] Linking C static library libimageutils.a
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
[ 50%] Built target imageutils
Scanning dependencies of target rknn_yolov8_demo
Scanning dependencies of target rknn_yolov8_demo_zero_copy
make[2]: Warning: File 'CMakeFiles/rknn_yolov8_demo.dir/depend.make' has modification time 2.2 s in the future
make[2]: Warning: File 'CMakeFiles/rknn_yolov8_demo_zero_copy.dir/depend.make' has modification time 2.2 s in the future
[ 62%] Building CXX object CMakeFiles/rknn_yolov8_demo.dir/main.cc.o
[ 68%] Building CXX object CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.o
[ 87%] Building CXX object CMakeFiles/rknn_yolov8_demo_zero_copy.dir/main.cc.o
[ 87%] Building CXX object CMakeFiles/rknn_yolov8_demo_zero_copy.dir/postprocess.cc.o
[ 87%] Building CXX object CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.o
[ 87%] Building CXX object CMakeFiles/rknn_yolov8_demo_zero_copy.dir/rknpu2/yolov8_zero_copy.cc.o
/workspace/yolo-rk3588/yolov8/cpp/postprocess.cc: In function 'char* coco_cls_to_name(int)':
/workspace/yolo-rk3588/yolov8/cpp/postprocess.cc:675:16: warning: ISO C++ forbids converting a string constant to 'char*' [-Wwrite-strings]
         return "null";
                ^~~~~~
/workspace/yolo-rk3588/yolov8/cpp/postprocess.cc:683:12: warning: ISO C++ forbids converting a string constant to 'char*' [-Wwrite-strings]
     return "null";
            ^~~~~~
/workspace/yolo-rk3588/yolov8/cpp/postprocess.cc: In function 'char* coco_cls_to_name(int)':
/workspace/yolo-rk3588/yolov8/cpp/postprocess.cc:675:16: warning: ISO C++ forbids converting a string constant to 'char*' [-Wwrite-strings]
         return "null";
                ^~~~~~
/workspace/yolo-rk3588/yolov8/cpp/postprocess.cc:683:12: warning: ISO C++ forbids converting a string constant to 'char*' [-Wwrite-strings]
     return "null";
            ^~~~~~
[ 93%] Linking CXX executable rknn_yolov8_demo
[100%] Linking CXX executable rknn_yolov8_demo_zero_copy
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
[100%] Built target rknn_yolov8_demo
[100%] Built target rknn_yolov8_demo_zero_copy
make[1]: warning:  Clock skew detected.  Your build may be incomplete.
make: warning:  Clock skew detected.  Your build may be incomplete.
make[2]: Warning: File 'utils.out/libimageutils.a' has modification time 0.82 s in the future
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
[ 12%] Built target imageutils
[ 25%] Built target imagedrawing
[ 37%] Built target fileutils
make[2]: Warning: File 'CMakeFiles/rknn_yolov8_demo_zero_copy.dir/depend.make' has modification time 0.58 s in the future
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
make[2]: Warning: File 'CMakeFiles/rknn_yolov8_demo_zero_copy.dir/depend.make' has modification time 0.51 s in the future
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
[ 62%] Built target rknn_yolov8_demo_zero_copy
make[2]: Warning: File 'CMakeFiles/rknn_yolov8_demo.dir/depend.make' has modification time 0.41 s in the future
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
make[2]: Warning: File 'CMakeFiles/rknn_yolov8_demo.dir/depend.make' has modification time 0.34 s in the future
make[2]: warning:  Clock skew detected.  Your build may be incomplete.
[ 87%] Built target rknn_yolov8_demo
[100%] Built target audioutils
Install the project...
-- Install configuration: "Release"
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/./rknn_yolov8_demo_zero_copy
-- Set runtime path of "/workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/./rknn_yolov8_demo_zero_copy" to "$ORIGIN/../lib"
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/./rknn_yolov8_demo
-- Set runtime path of "/workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/./rknn_yolov8_demo" to "$ORIGIN/../lib"
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/model/bus.jpg
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/model/coco_80_labels_list.txt
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/model/yolov8.rknn
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/lib/librknnrt.so
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/lib/librga.so
