===================================
BUILD_DEMO_NAME:   yolov5
BUILD_DEMO_PATH:   /workspace/yolo-rk3588/yolov5/cpp
TARGET_SOC:        rk3588
TARGET_ARCH:       aarch64
BUILD_TYPE:        Release
ENABLE_ASAN:       OFF
DISABLE_RGA:       OFF
DISABLE_LIBJPEG:   OFF
CC:                /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
CXX:               /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++
INPUT_PATH:        /workspace/yolo-rk3588/result/yolov5
OUTPUT_PATH:       /workspace/yolo-rk3588/result/yolov5/deploy
INSTALL_DIR:       /workspace/yolo-rk3588/result/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo
BUILD_DIR:         /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release
===================================
-- The C compiler identification is GNU 6.3.1
-- The CXX compiler identification is GNU 6.3.1
-- Check for working C compiler: /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
-- Check for working C compiler: /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc -- works
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Detecting C compile features
-- Detecting C compile features - done
-- Check for working CXX compiler: /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++
-- Check for working CXX compiler: /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ -- works
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed
-- Check if compiler accepts -pthread
-- Check if compiler accepts -pthread - yes
-- Found Threads: TRUE  
-- Configuring done
-- Generating done
CMake Warning:
  Manually-specified variables were not used by the project:

    OpenCV_DIR


-- Build files have been written to: /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release
Scanning dependencies of target fileutils
Scanning dependencies of target audioutils
Scanning dependencies of target imagedrawing
Scanning dependencies of target imageutils
[  8%] Building C object utils.out/CMakeFiles/fileutils.dir/file_utils.c.o
[ 25%] Building C object utils.out/CMakeFiles/imagedrawing.dir/image_drawing.c.o
[ 25%] Building C object utils.out/CMakeFiles/audioutils.dir/audio_utils.c.o
[ 33%] Building C object utils.out/CMakeFiles/imageutils.dir/image_utils.c.o
/workspace/yolo-rk3588/utils/image_drawing.c: In function 'rbbox_to_corners':
/workspace/yolo-rk3588/utils/image_drawing.c:1541:19: warning: implicit declaration of function 'cos' [-Wimplicit-function-declaration]
     float a_cos = cos(angle);
                   ^~~
/workspace/yolo-rk3588/utils/image_drawing.c:1541:19: warning: incompatible implicit declaration of built-in function 'cos'
/workspace/yolo-rk3588/utils/image_drawing.c:1541:19: note: include '<math.h>' or provide a declaration of 'cos'
/workspace/yolo-rk3588/utils/image_drawing.c:1542:19: warning: implicit declaration of function 'sin' [-Wimplicit-function-declaration]
     float a_sin = sin(angle);
                   ^~~
/workspace/yolo-rk3588/utils/image_drawing.c:1542:19: warning: incompatible implicit declaration of built-in function 'sin'
/workspace/yolo-rk3588/utils/image_drawing.c:1542:19: note: include '<math.h>' or provide a declaration of 'sin'
[ 41%] Linking C static library libfileutils.a
[ 50%] Linking C static library libaudioutils.a
/workspace/yolo-rk3588/utils/image_utils.c: In function 'write_image':
/workspace/yolo-rk3588/utils/image_utils.c:290:35: warning: passing argument 1 of 'get_image_size' discards 'const' qualifier from pointer target type [-Wdiscarded-qualifiers]
         int size = get_image_size(img);
                                   ^~~
In file included from /workspace/yolo-rk3588/utils/image_utils.c:19:0:
/workspace/yolo-rk3588/utils/image_utils.h:67:5: note: expected 'image_buffer_t * {aka struct <anonymous> *}' but argument is of type 'const image_buffer_t * {aka const struct <anonymous> *}'
 int get_image_size(image_buffer_t* image);
     ^~~~~~~~~~~~~~
/workspace/yolo-rk3588/utils/image_utils.c: In function 'convert_image_rga':
/workspace/yolo-rk3588/utils/image_utils.c:634:27: warning: initialization from incompatible pointer type [-Wincompatible-pointer-types]
         char* p_imcolor = &imcolor;
                           ^
[ 50%] Built target audioutils
[ 50%] Built target fileutils
[ 58%] Linking C static library libimagedrawing.a
[ 58%] Built target imagedrawing
[ 66%] Linking C static library libimageutils.a
[ 66%] Built target imageutils
Scanning dependencies of target rknn_yolov5_demo
[ 83%] Building CXX object CMakeFiles/rknn_yolov5_demo.dir/main.cc.o
[ 83%] Building CXX object CMakeFiles/rknn_yolov5_demo.dir/postprocess.cc.o
[ 91%] Building CXX object CMakeFiles/rknn_yolov5_demo.dir/rknpu2/yolov5.cc.o
/workspace/yolo-rk3588/yolov5/cpp/postprocess.cc: In function 'char* coco_cls_to_name(int)':
/workspace/yolo-rk3588/yolov5/cpp/postprocess.cc:577:16: warning: ISO C++ forbids converting a string constant to 'char*' [-Wwrite-strings]
         return "null";
                ^~~~~~
/workspace/yolo-rk3588/yolov5/cpp/postprocess.cc:585:12: warning: ISO C++ forbids converting a string constant to 'char*' [-Wwrite-strings]
     return "null";
            ^~~~~~
[100%] Linking CXX executable rknn_yolov5_demo
[100%] Built target rknn_yolov5_demo
