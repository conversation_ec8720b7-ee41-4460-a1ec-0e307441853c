===================================
BUILD_DEMO_NAME:   yolov5
BUILD_DEMO_PATH:   /workspace/yolo-rk3588/yolov5/cpp
TARGET_SOC:        rk3588
TARGET_ARCH:       aarch64
BUILD_TYPE:        Release
ENABLE_ASAN:       OFF
DISABLE_RGA:       OFF
DISABLE_LIBJPEG:   OFF
CC:                /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
CXX:               /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++
INPUT_PATH:        /workspace/yolo-rk3588/result/yolov5
OUTPUT_PATH:       /workspace/yolo-rk3588/result/yolov5/deploy
INSTALL_DIR:       /workspace/yolo-rk3588/result/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo
BUILD_DIR:         /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release
===================================
-- Configuring done
-- Generating done
-- Build files have been written to: /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release
[ 66%] Built target imagedrawing
[ 66%] Built target fileutils
[ 66%] Built target audioutils
[ 66%] Built target imageutils
[100%] Built target rknn_yolov5_demo
[ 16%] Built target fileutils
[ 33%] Built target imageutils
[ 50%] Built target imagedrawing
[ 83%] Built target rknn_yolov5_demo
[100%] Built target audioutils
Install the project...
-- Install configuration: "Release"
-- Installing: /workspace/yolo-rk3588/result/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo/./rknn_yolov5_demo
-- Set runtime path of "/workspace/yolo-rk3588/result/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo/./rknn_yolov5_demo" to "$ORIGIN/lib"
-- Installing: /workspace/yolo-rk3588/result/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo/./model/bus.jpg
-- Installing: /workspace/yolo-rk3588/result/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo/./model/coco_80_labels_list.txt
-- Installing: /workspace/yolo-rk3588/result/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo/model/yolov5.rknn
-- Installing: /workspace/yolo-rk3588/result/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo/lib/librknnrt.so
-- Installing: /workspace/yolo-rk3588/result/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo/lib/librga.so
