# AutoBit 模型转换工具 v2.0

## 📋 项目概述

AutoBit 模型转换工具是一个专业的YOLO模型转换应用，支持将PyTorch和ONNX格式的YOLOv5/v8模型转换为RK3588平台优化的RKNN格式。

### ✨ 主要特性

- 🔐 **双模式激活系统**: 支持生产模式（硬件绑定）和调试模式（离线验证）
- 🎨 **现代化UI设计**: 采用现代化界面设计，提供优秀的用户体验
- 🔒 **硬件指纹绑定**: 基于多种硬件信息生成唯一设备标识
- 🛡️ **加密许可证存储**: 本地许可证采用加密存储，确保安全性
- 🚀 **Docker集成转换**: 集成Docker环境，确保转换过程的一致性
- 📊 **实时转换监控**: 提供详细的转换日志和进度监控

### 🏗️ 系统架构

```
AutoBit/
├── ModelConverter/           # Qt应用程序
│   ├── main.cpp             # 程序入口
│   ├── activationwindow.*   # 激活窗口（现代化UI）
│   ├── conversionwindow.*   # 转换窗口（现代化UI）
│   ├── HardwareFingerprint.*# 硬件指纹生成器
│   ├── LicenseManager.*     # 许可证管理器
│   └── ModelConverter.pro  # Qt项目配置
├── build_and_run.sh         # 编译启动脚本
└── README.md               # 本文档
```

## 🔐 激活系统详解

### 双模式设计

#### 1. 生产模式（Production Mode）
- **硬件绑定**: 基于CPU、主板、MAC地址、硬盘序列号等生成设备指纹
- **在线验证**: 连接激活服务器进行实时验证
- **加密存储**: 许可证本地加密存储，防止篡改
- **安全性高**: 适用于正式部署环境

#### 2. 调试模式（Debug Mode）
- **离线验证**: 无需网络连接，使用预设激活码
- **快速激活**: 简化验证流程，便于开发调试
- **测试友好**: 支持多个测试激活码
- **开发便利**: 适用于开发和测试环境


