# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspace/yolo-rk3588/yolov5/cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/rknn_yolov5_demo.dir/all
all: 3rdparty.out/all
all: utils.out/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall: 3rdparty.out/preinstall
preinstall: utils.out/preinstall

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/rknn_yolov5_demo.dir/clean
clean: 3rdparty.out/clean
clean: utils.out/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty.out

# Recursive "all" directory target.
3rdparty.out/all:

.PHONY : 3rdparty.out/all

# Recursive "preinstall" directory target.
3rdparty.out/preinstall:

.PHONY : 3rdparty.out/preinstall

# Recursive "clean" directory target.
3rdparty.out/clean:

.PHONY : 3rdparty.out/clean

#=============================================================================
# Directory level rules for directory utils.out

# Recursive "all" directory target.
utils.out/all: utils.out/CMakeFiles/audioutils.dir/all
utils.out/all: utils.out/CMakeFiles/imageutils.dir/all
utils.out/all: utils.out/CMakeFiles/imagedrawing.dir/all
utils.out/all: utils.out/CMakeFiles/fileutils.dir/all

.PHONY : utils.out/all

# Recursive "preinstall" directory target.
utils.out/preinstall:

.PHONY : utils.out/preinstall

# Recursive "clean" directory target.
utils.out/clean: utils.out/CMakeFiles/audioutils.dir/clean
utils.out/clean: utils.out/CMakeFiles/imageutils.dir/clean
utils.out/clean: utils.out/CMakeFiles/imagedrawing.dir/clean
utils.out/clean: utils.out/CMakeFiles/fileutils.dir/clean

.PHONY : utils.out/clean

#=============================================================================
# Target rules for target CMakeFiles/rknn_yolov5_demo.dir

# All Build rule for target.
CMakeFiles/rknn_yolov5_demo.dir/all: utils.out/CMakeFiles/imageutils.dir/all
CMakeFiles/rknn_yolov5_demo.dir/all: utils.out/CMakeFiles/imagedrawing.dir/all
CMakeFiles/rknn_yolov5_demo.dir/all: utils.out/CMakeFiles/fileutils.dir/all
	$(MAKE) -f CMakeFiles/rknn_yolov5_demo.dir/build.make CMakeFiles/rknn_yolov5_demo.dir/depend
	$(MAKE) -f CMakeFiles/rknn_yolov5_demo.dir/build.make CMakeFiles/rknn_yolov5_demo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=9,10,11,12 "Built target rknn_yolov5_demo"
.PHONY : CMakeFiles/rknn_yolov5_demo.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/rknn_yolov5_demo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 10
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/rknn_yolov5_demo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 0
.PHONY : CMakeFiles/rknn_yolov5_demo.dir/rule

# Convenience name for target.
rknn_yolov5_demo: CMakeFiles/rknn_yolov5_demo.dir/rule

.PHONY : rknn_yolov5_demo

# clean rule for target.
CMakeFiles/rknn_yolov5_demo.dir/clean:
	$(MAKE) -f CMakeFiles/rknn_yolov5_demo.dir/build.make CMakeFiles/rknn_yolov5_demo.dir/clean
.PHONY : CMakeFiles/rknn_yolov5_demo.dir/clean

#=============================================================================
# Target rules for target utils.out/CMakeFiles/audioutils.dir

# All Build rule for target.
utils.out/CMakeFiles/audioutils.dir/all:
	$(MAKE) -f utils.out/CMakeFiles/audioutils.dir/build.make utils.out/CMakeFiles/audioutils.dir/depend
	$(MAKE) -f utils.out/CMakeFiles/audioutils.dir/build.make utils.out/CMakeFiles/audioutils.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=1,2 "Built target audioutils"
.PHONY : utils.out/CMakeFiles/audioutils.dir/all

# Build rule for subdir invocation for target.
utils.out/CMakeFiles/audioutils.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 utils.out/CMakeFiles/audioutils.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 0
.PHONY : utils.out/CMakeFiles/audioutils.dir/rule

# Convenience name for target.
audioutils: utils.out/CMakeFiles/audioutils.dir/rule

.PHONY : audioutils

# clean rule for target.
utils.out/CMakeFiles/audioutils.dir/clean:
	$(MAKE) -f utils.out/CMakeFiles/audioutils.dir/build.make utils.out/CMakeFiles/audioutils.dir/clean
.PHONY : utils.out/CMakeFiles/audioutils.dir/clean

#=============================================================================
# Target rules for target utils.out/CMakeFiles/imageutils.dir

# All Build rule for target.
utils.out/CMakeFiles/imageutils.dir/all:
	$(MAKE) -f utils.out/CMakeFiles/imageutils.dir/build.make utils.out/CMakeFiles/imageutils.dir/depend
	$(MAKE) -f utils.out/CMakeFiles/imageutils.dir/build.make utils.out/CMakeFiles/imageutils.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=7,8 "Built target imageutils"
.PHONY : utils.out/CMakeFiles/imageutils.dir/all

# Build rule for subdir invocation for target.
utils.out/CMakeFiles/imageutils.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 utils.out/CMakeFiles/imageutils.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 0
.PHONY : utils.out/CMakeFiles/imageutils.dir/rule

# Convenience name for target.
imageutils: utils.out/CMakeFiles/imageutils.dir/rule

.PHONY : imageutils

# clean rule for target.
utils.out/CMakeFiles/imageutils.dir/clean:
	$(MAKE) -f utils.out/CMakeFiles/imageutils.dir/build.make utils.out/CMakeFiles/imageutils.dir/clean
.PHONY : utils.out/CMakeFiles/imageutils.dir/clean

#=============================================================================
# Target rules for target utils.out/CMakeFiles/imagedrawing.dir

# All Build rule for target.
utils.out/CMakeFiles/imagedrawing.dir/all:
	$(MAKE) -f utils.out/CMakeFiles/imagedrawing.dir/build.make utils.out/CMakeFiles/imagedrawing.dir/depend
	$(MAKE) -f utils.out/CMakeFiles/imagedrawing.dir/build.make utils.out/CMakeFiles/imagedrawing.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=5,6 "Built target imagedrawing"
.PHONY : utils.out/CMakeFiles/imagedrawing.dir/all

# Build rule for subdir invocation for target.
utils.out/CMakeFiles/imagedrawing.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 utils.out/CMakeFiles/imagedrawing.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 0
.PHONY : utils.out/CMakeFiles/imagedrawing.dir/rule

# Convenience name for target.
imagedrawing: utils.out/CMakeFiles/imagedrawing.dir/rule

.PHONY : imagedrawing

# clean rule for target.
utils.out/CMakeFiles/imagedrawing.dir/clean:
	$(MAKE) -f utils.out/CMakeFiles/imagedrawing.dir/build.make utils.out/CMakeFiles/imagedrawing.dir/clean
.PHONY : utils.out/CMakeFiles/imagedrawing.dir/clean

#=============================================================================
# Target rules for target utils.out/CMakeFiles/fileutils.dir

# All Build rule for target.
utils.out/CMakeFiles/fileutils.dir/all:
	$(MAKE) -f utils.out/CMakeFiles/fileutils.dir/build.make utils.out/CMakeFiles/fileutils.dir/depend
	$(MAKE) -f utils.out/CMakeFiles/fileutils.dir/build.make utils.out/CMakeFiles/fileutils.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=3,4 "Built target fileutils"
.PHONY : utils.out/CMakeFiles/fileutils.dir/all

# Build rule for subdir invocation for target.
utils.out/CMakeFiles/fileutils.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 2
	$(MAKE) -f CMakeFiles/Makefile2 utils.out/CMakeFiles/fileutils.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 0
.PHONY : utils.out/CMakeFiles/fileutils.dir/rule

# Convenience name for target.
fileutils: utils.out/CMakeFiles/fileutils.dir/rule

.PHONY : fileutils

# clean rule for target.
utils.out/CMakeFiles/fileutils.dir/clean:
	$(MAKE) -f utils.out/CMakeFiles/fileutils.dir/build.make utils.out/CMakeFiles/fileutils.dir/clean
.PHONY : utils.out/CMakeFiles/fileutils.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

