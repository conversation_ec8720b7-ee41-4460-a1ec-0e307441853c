# Qt项目配置 - Ubuntu WSL2环境
# 原始配置已备份到 ModelConverter.pro.backup

QT       += core gui widgets
TARGET   = ModelConverter
CONFIG   += c++17

# 禁用 sized-deallocation，防止库里对 operator delete(void*,size_t) 的依赖
QMAKE_CXXFLAGS += -fno-sized-deallocation

# 动态库导入声明
DEFINES += SF_CORE_LS_EXPORT=Q_DECL_IMPORT

# 使用系统Qt5库（通过apt安装）
# 不需要指定QMAKE_PREFIX_PATH，使用系统默认路径

# 链接第三方的 sf-core-ls 动态库（.so 放在项目根目录）
LIBS += -L. -lsf-core-ls

# 设置运行时 rpath，让可执行加载时在项目目录寻找 .so
QMAKE_RPATHDIR += $$PWD

# 资源、源码、头文件列表
#RESOURCES += ModelConverter.qrc

SOURCES  += main.cpp \
            activationwindow.cpp \
            conversionwindow.cpp \
            sized_delete.cpp

HEADERS  += activationwindow.h \
            conversionwindow.h