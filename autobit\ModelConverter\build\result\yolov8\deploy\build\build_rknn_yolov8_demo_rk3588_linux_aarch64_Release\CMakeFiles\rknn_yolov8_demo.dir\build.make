# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspace/yolo-rk3588/yolov8/cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release

# Include any dependencies generated for this target.
include CMakeFiles/rknn_yolov8_demo.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/rknn_yolov8_demo.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/rknn_yolov8_demo.dir/flags.make

CMakeFiles/rknn_yolov8_demo.dir/main.cc.o: CMakeFiles/rknn_yolov8_demo.dir/flags.make
CMakeFiles/rknn_yolov8_demo.dir/main.cc.o: /workspace/yolo-rk3588/yolov8/cpp/main.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/rknn_yolov8_demo.dir/main.cc.o"
	/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/rknn_yolov8_demo.dir/main.cc.o -c /workspace/yolo-rk3588/yolov8/cpp/main.cc

CMakeFiles/rknn_yolov8_demo.dir/main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rknn_yolov8_demo.dir/main.cc.i"
	/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/yolo-rk3588/yolov8/cpp/main.cc > CMakeFiles/rknn_yolov8_demo.dir/main.cc.i

CMakeFiles/rknn_yolov8_demo.dir/main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rknn_yolov8_demo.dir/main.cc.s"
	/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/yolo-rk3588/yolov8/cpp/main.cc -o CMakeFiles/rknn_yolov8_demo.dir/main.cc.s

CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.o: CMakeFiles/rknn_yolov8_demo.dir/flags.make
CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.o: /workspace/yolo-rk3588/yolov8/cpp/postprocess.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.o"
	/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.o -c /workspace/yolo-rk3588/yolov8/cpp/postprocess.cc

CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.i"
	/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/yolo-rk3588/yolov8/cpp/postprocess.cc > CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.i

CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.s"
	/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/yolo-rk3588/yolov8/cpp/postprocess.cc -o CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.s

CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.o: CMakeFiles/rknn_yolov8_demo.dir/flags.make
CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.o: /workspace/yolo-rk3588/yolov8/cpp/rknpu2/yolov8.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.o"
	/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.o -c /workspace/yolo-rk3588/yolov8/cpp/rknpu2/yolov8.cc

CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.i"
	/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/yolo-rk3588/yolov8/cpp/rknpu2/yolov8.cc > CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.i

CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.s"
	/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/yolo-rk3588/yolov8/cpp/rknpu2/yolov8.cc -o CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.s

# Object files for target rknn_yolov8_demo
rknn_yolov8_demo_OBJECTS = \
"CMakeFiles/rknn_yolov8_demo.dir/main.cc.o" \
"CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.o" \
"CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.o"

# External object files for target rknn_yolov8_demo
rknn_yolov8_demo_EXTERNAL_OBJECTS =

rknn_yolov8_demo: CMakeFiles/rknn_yolov8_demo.dir/main.cc.o
rknn_yolov8_demo: CMakeFiles/rknn_yolov8_demo.dir/postprocess.cc.o
rknn_yolov8_demo: CMakeFiles/rknn_yolov8_demo.dir/rknpu2/yolov8.cc.o
rknn_yolov8_demo: CMakeFiles/rknn_yolov8_demo.dir/build.make
rknn_yolov8_demo: utils.out/libimageutils.a
rknn_yolov8_demo: utils.out/libfileutils.a
rknn_yolov8_demo: utils.out/libimagedrawing.a
rknn_yolov8_demo: /workspace/yolo-rk3588/3rdparty/rknpu2/Linux/aarch64/librknnrt.so
rknn_yolov8_demo: /workspace/yolo-rk3588/3rdparty/librga/Linux/aarch64/librga.a
rknn_yolov8_demo: /workspace/yolo-rk3588/3rdparty/jpeg_turbo/Linux/aarch64/libturbojpeg.a
rknn_yolov8_demo: CMakeFiles/rknn_yolov8_demo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable rknn_yolov8_demo"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/rknn_yolov8_demo.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/rknn_yolov8_demo.dir/build: rknn_yolov8_demo

.PHONY : CMakeFiles/rknn_yolov8_demo.dir/build

CMakeFiles/rknn_yolov8_demo.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/rknn_yolov8_demo.dir/cmake_clean.cmake
.PHONY : CMakeFiles/rknn_yolov8_demo.dir/clean

CMakeFiles/rknn_yolov8_demo.dir/depend:
	cd /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /workspace/yolo-rk3588/yolov8/cpp /workspace/yolo-rk3588/yolov8/cpp /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles/rknn_yolov8_demo.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/rknn_yolov8_demo.dir/depend

