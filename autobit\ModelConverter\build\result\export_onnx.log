[34m[1mexport: [0mdata=yolov5/data/coco128.yaml, weights=['/workspace/yolo-rk3588/models/yolov5.onnx'], result=/workspace/yolo-rk3588/result/yolov5/yolov5.onnx, imgsz=[640, 640], batch_size=1, device=cpu, half=False, inplace=False, keras=False, optimize=False, int8=False, dynamic=False, simplify=False, opset=12, verbose=False, workspace=4, nms=False, agnostic_nms=False, topk_per_class=100, topk_all=100, iou_thres=0.45, conf_thres=0.25, include=['onnx'], rknpu=True
YOLOv5 🚀 v4.0-1657-gd25a075 Python-3.8.13 torch-1.10.1+cu102 CPU

Traceback (most recent call last):
  File "/workspace/yolo-rk3588/yolov5/export.py", line 727, in <module>
    main(opt)
  File "/workspace/yolo-rk3588/yolov5/export.py", line 721, in main
    run(**vars(opt))
  File "/opt/conda/envs/RKNN-Toolkit2/lib/python3.8/site-packages/torch/autograd/grad_mode.py", line 28, in decorate_context
    return func(*args, **kwargs)
  File "/workspace/yolo-rk3588/yolov5/export.py", line 535, in run
    model = attempt_load(weights, device=device, inplace=True, fuse=True)  # load FP32 model
  File "/workspace/yolo-rk3588/yolov5/models/experimental.py", line 80, in attempt_load
    ckpt = torch.load(attempt_download(w), map_location='cpu')  # load
  File "/opt/conda/envs/RKNN-Toolkit2/lib/python3.8/site-packages/torch/serialization.py", line 608, in load
    return _legacy_load(opened_file, map_location, pickle_module, **pickle_load_args)
  File "/opt/conda/envs/RKNN-Toolkit2/lib/python3.8/site-packages/torch/serialization.py", line 777, in _legacy_load
    magic_number = pickle_module.load(f, **pickle_load_args)
_pickle.UnpicklingError: invalid load key, '\x08'.
