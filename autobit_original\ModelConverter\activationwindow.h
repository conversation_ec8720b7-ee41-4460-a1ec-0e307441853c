#ifndef ACTIVATIONWINDOW_H
#define ACTIVATIONWINDOW_H

#include <QWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include "conversionwindow.h"
// #include "SFCoreIntf.h"  // 注释掉许可证库头文件

class ActivationWindow : public QWidget
{
    Q_OBJECT
public:
    explicit ActivationWindow(QWidget *parent = nullptr);

private slots:
    void onActivateClicked();

private:
    QLineEdit *licenseEdit;       // 用户输入的许可证号
    QPushButton *activateButton;  // 激活按钮
    QLabel *statusLabel;          // 激活状态提示

    ConversionWindow *convWindow; // 成功后打开的转换窗口
};

#endif