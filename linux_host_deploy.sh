#!/bin/bash
# YOLO模型转换工具 - Linux主机环境完整部署脚本
# 基于autobit_back项目，适用于Linux服务器/工作站环境
# 支持完整的模型转换、压缩、编译和部署流程

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }
echo_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }
echo_cmd() { echo -e "${CYAN}[CMD]${NC} $1"; }

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${SCRIPT_DIR}"
AUTOBIT_BACK_DIR="${PROJECT_ROOT}/autobit_back"
YOLO_RK3588_DIR="${AUTOBIT_BACK_DIR}/yolo-rk3588"
MODEL_CONVERTER_DIR="${AUTOBIT_BACK_DIR}/ModelConverter"

# 默认配置
DEFAULT_QT_PATH="/home/<USER>/qt512/5.12.6/gcc_64"
DOCKER_IMAGE_NAME="model-converter:latest"
DOCKER_TAR_FILE="model-converter.tar"

# 工作目录
WORK_DIR="${PROJECT_ROOT}/workspace"
MODELS_DIR="${WORK_DIR}/models"
RESULT_DIR="${WORK_DIR}/result"
LOGS_DIR="${WORK_DIR}/logs"

echo_step "=== YOLO模型转换工具 - Linux主机环境部署脚本 ==="
echo_info "项目根目录: ${PROJECT_ROOT}"
echo_info "工作目录: ${WORK_DIR}"

# 函数：检查系统环境
check_system_environment() {
    echo_step "1. 检查系统环境..."
    
    # 检查操作系统
    if ! uname -a | grep -q "Linux"; then
        echo_error "此脚本需要在Linux环境中运行"
        exit 1
    fi
    echo_success "✓ Linux环境检查通过"
    
    # 检查系统架构
    ARCH=$(uname -m)
    echo_info "系统架构: ${ARCH}"
    if [[ "${ARCH}" != "x86_64" ]]; then
        echo_warning "⚠️ 建议使用x86_64架构，当前架构: ${ARCH}"
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    echo_info "系统内存: ${MEMORY_GB}GB"
    if [[ ${MEMORY_GB} -lt 8 ]]; then
        echo_warning "⚠️ 建议至少8GB内存，当前: ${MEMORY_GB}GB"
    fi
    
    # 检查磁盘空间
    DISK_GB=$(df -BG "${PROJECT_ROOT}" | awk 'NR==2{print $4}' | sed 's/G//')
    echo_info "可用磁盘空间: ${DISK_GB}GB"
    if [[ ${DISK_GB} -lt 20 ]]; then
        echo_warning "⚠️ 建议至少20GB可用空间，当前: ${DISK_GB}GB"
    fi
    
    echo_success "系统环境检查完成"
}

# 函数：检查Docker环境
check_docker_environment() {
    echo_step "2. 检查Docker环境..."
    
    # 检查Docker是否安装
    if ! command -v docker &> /dev/null; then
        echo_error "Docker未安装，请先安装Docker"
        echo_info "Ubuntu/Debian安装命令:"
        echo_cmd "curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
        exit 1
    fi
    echo_success "✓ Docker已安装"
    
    # 检查Docker服务状态
    if ! docker info &> /dev/null; then
        echo_error "Docker服务未运行，请启动Docker服务"
        echo_cmd "sudo systemctl start docker"
        exit 1
    fi
    echo_success "✓ Docker服务正常运行"
    
    # 检查Docker权限
    if ! docker ps &> /dev/null; then
        echo_warning "⚠️ 当前用户可能没有Docker权限"
        echo_info "添加用户到docker组:"
        echo_cmd "sudo usermod -aG docker \$USER && newgrp docker"
    fi
    
    # 显示Docker版本信息
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    echo_info "Docker版本: ${DOCKER_VERSION}"
    
    echo_success "Docker环境检查完成"
}

# 函数：检查Qt环境
check_qt_environment() {
    echo_step "3. 检查Qt环境..."
    
    # 检查Qt安装路径
    if [[ -n "${QT_PATH}" ]]; then
        QT_INSTALL_PATH="${QT_PATH}"
    elif [[ -d "${DEFAULT_QT_PATH}" ]]; then
        QT_INSTALL_PATH="${DEFAULT_QT_PATH}"
    else
        echo_warning "⚠️ 未找到Qt 5.12.6安装路径"
        echo_info "请设置QT_PATH环境变量或确保Qt安装在: ${DEFAULT_QT_PATH}"
        echo_info "或者使用系统包管理器安装Qt5:"
        echo_cmd "sudo apt install -y qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools"
        
        # 尝试使用系统Qt
        if command -v qmake &> /dev/null; then
            QT_INSTALL_PATH=$(qmake -query QT_INSTALL_PREFIX)
            echo_info "使用系统Qt路径: ${QT_INSTALL_PATH}"
        else
            echo_error "未找到可用的Qt环境"
            exit 1
        fi
    fi
    
    echo_success "✓ Qt安装路径: ${QT_INSTALL_PATH}"
    
    # 检查Qt版本
    if [[ -f "${QT_INSTALL_PATH}/bin/qmake" ]]; then
        QT_VERSION=$(${QT_INSTALL_PATH}/bin/qmake --version | grep "Qt version" | cut -d' ' -f4)
        echo_info "Qt版本: ${QT_VERSION}"
    fi
    
    # 检查显示环境
    if [[ -z "${DISPLAY}" ]]; then
        echo_warning "⚠️ 未设置DISPLAY环境变量"
        echo_info "如果需要GUI界面，请设置DISPLAY变量"
        echo_info "本地显示: export DISPLAY=:0"
        echo_info "远程X11转发: ssh -X user@host"
    else
        echo_success "✓ DISPLAY环境变量已设置: ${DISPLAY}"
    fi
    
    echo_success "Qt环境检查完成"
}

# 函数：加载Docker镜像
load_docker_image() {
    echo_step "4. 加载Docker镜像..."
    
    # 检查镜像文件是否存在
    if [[ ! -f "${PROJECT_ROOT}/${DOCKER_TAR_FILE}" ]]; then
        echo_error "未找到Docker镜像文件: ${DOCKER_TAR_FILE}"
        echo_info "请确保 ${DOCKER_TAR_FILE} 文件在项目根目录中"
        exit 1
    fi
    
    # 检查镜像是否已加载
    if docker images | grep -q "model-converter"; then
        echo_info "Docker镜像已存在，跳过加载"
        docker images | grep model-converter
    else
        echo_info "正在加载Docker镜像..."
        docker load -i "${PROJECT_ROOT}/${DOCKER_TAR_FILE}"
        echo_success "✓ Docker镜像加载完成"
    fi
    
    # 验证镜像
    if ! docker images | grep -q "model-converter"; then
        echo_error "Docker镜像加载失败"
        exit 1
    fi
    
    echo_success "Docker镜像准备完成"
}

# 函数：创建工作目录
setup_workspace() {
    echo_step "5. 创建工作目录..."
    
    # 创建必要的目录结构
    mkdir -p "${MODELS_DIR}"
    mkdir -p "${RESULT_DIR}"
    mkdir -p "${LOGS_DIR}"
    
    echo_success "✓ 工作目录创建完成"
    echo_info "模型目录: ${MODELS_DIR}"
    echo_info "结果目录: ${RESULT_DIR}"
    echo_info "日志目录: ${LOGS_DIR}"
}

# 函数：编译Qt应用程序
compile_qt_application() {
    echo_step "6. 编译Qt应用程序..."
    
    # 检查ModelConverter目录
    if [[ ! -d "${MODEL_CONVERTER_DIR}" ]]; then
        echo_error "未找到ModelConverter目录: ${MODEL_CONVERTER_DIR}"
        exit 1
    fi
    
    cd "${MODEL_CONVERTER_DIR}"
    
    # 创建build目录
    mkdir -p build
    cd build
    
    # 设置Qt环境变量
    if [[ -n "${QT_INSTALL_PATH}" && -f "${QT_INSTALL_PATH}/bin/qmake" ]]; then
        export PATH="${QT_INSTALL_PATH}/bin:${PATH}"
        export LD_LIBRARY_PATH="${QT_INSTALL_PATH}/lib:${LD_LIBRARY_PATH}"
    fi
    
    # 检查是否需要重新编译
    if [[ -f "ModelConverter" ]]; then
        echo_info "发现已编译的程序，检查是否需要重新编译..."
        if [[ "../ModelConverter.pro" -nt "ModelConverter" ]]; then
            echo_info "源码有更新，重新编译..."
            make clean || true
        else
            echo_info "程序是最新的，跳过编译"
            echo_success "Qt应用程序准备完成"
            return 0
        fi
    fi
    
    # 编译项目
    echo_info "正在编译Qt项目..."
    qmake ../ModelConverter.pro
    make -j$(nproc)
    
    if [[ $? -eq 0 ]]; then
        echo_success "✓ Qt项目编译成功"
    else
        echo_error "Qt项目编译失败"
        exit 1
    fi
    
    # 复制动态库文件
    echo_info "复制动态库文件..."
    if [[ -f "../libsf-core-ls.so" ]]; then
        cp "../libsf-core-ls.so" .
        echo_success "✓ 动态库文件复制完成"
    else
        echo_warning "⚠️ 未找到动态库文件 libsf-core-ls.so"
    fi
    
    echo_success "Qt应用程序编译完成"
}

# 主函数
main() {
    echo_info "开始执行Linux主机环境部署流程..."
    
    # 执行各个检查和设置步骤
    check_system_environment
    check_docker_environment
    check_qt_environment
    load_docker_image
    setup_workspace
    compile_qt_application
    
    echo_step "=== 部署完成 ==="
    echo_success "🎉 YOLO模型转换工具部署成功！"
    
    echo_info "=== 使用说明 ==="
    echo_info "1. GUI方式启动:"
    echo_cmd "   cd ${MODEL_CONVERTER_DIR}/build && ./ModelConverter"
    echo_info "2. 命令行方式转换:"
    echo_cmd "   ./linux_model_convert.sh --model yolov8 --weight /path/to/model.pt"
    echo_info "3. 激活参数（测试用）:"
    echo_info "   设备号: 50bf70582c5ea2ac7502656f8cfb522e"
    echo_info "   产品号: 40201"
    echo_info "   激活码: E76G-JEQR-EQRA-T7ZW"
    
    echo_info "=== 目录结构 ==="
    echo_info "工作目录: ${WORK_DIR}"
    echo_info "模型文件: ${MODELS_DIR}"
    echo_info "转换结果: ${RESULT_DIR}"
    echo_info "运行日志: ${LOGS_DIR}"
    
    echo_success "部署脚本执行完成！"
}

# 执行主函数
main "$@"
