#include "conversionwindow.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QFileDialog>
#include <QDesktopServices>
#include <QDir>
#include <QCoreApplication>
#include <QFile>
#include <QFileInfo>
#include <QStringList>
#include <QUrl>

ConversionWindow::ConversionWindow(QWidget *parent)
    : QWidget(parent), process(new QProcess(this))
{
    setupUI();
    setupConnections();
}

void ConversionWindow::setupUI()
{
    modelTypeCombo = new QComboBox(this);
    modelTypeCombo->addItems({"yolov5", "yolov8"});

    modelPathEdit = new QLineEdit(this);
    browseButton = new QPushButton("浏览", this);
    convertButton = new QPushButton("转换", this);
    resultDisplay = new QTextEdit(this);
    resultDisplay->setReadOnly(true);

    statusLabel = new QLabel("等待转换", this);
    spinnerLabel = new QLabel(this);
    spinnerLabel->setFixedWidth(20);
    spinnerLabel->setAlignment(Qt::AlignCenter);

    QHBoxLayout *topLayout = new QHBoxLayout;
    topLayout->addWidget(modelTypeCombo);
    topLayout->addWidget(modelPathEdit);
    topLayout->addWidget(browseButton);
    topLayout->addWidget(convertButton);
    topLayout->addWidget(spinnerLabel);
    topLayout->addWidget(statusLabel);

    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addLayout(topLayout);
    mainLayout->addWidget(resultDisplay);

    setLayout(mainLayout);
}

void ConversionWindow::setupConnections()
{
    connect(browseButton, &QPushButton::clicked, this, &ConversionWindow::onBrowseClicked);
    connect(convertButton, &QPushButton::clicked, this, &ConversionWindow::onConvertClicked);
    connect(process, &QProcess::readyReadStandardOutput, this, &ConversionWindow::onProcessOutput);
    connect(process, QOverload<int>::of(&QProcess::finished), this, &ConversionWindow::onProcessFinished);
    connect(process, &QProcess::readyReadStandardError, this, &ConversionWindow::onProcessErrorOutput);

}

void ConversionWindow::onBrowseClicked()
{
    QString filePath = QFileDialog::getOpenFileName(this, QString::fromUtf8("选择模型文件"), "", "Model Files (*.pt *.onnx)");
    if (!filePath.isEmpty()) {
        modelPathEdit->setText(filePath);
    }
}

void ConversionWindow::onConvertClicked() {
    convertButton->setEnabled(false);
    convertButton->setText("转换中...");

    QString modelPath = modelPathEdit->text().trimmed();
    if (modelPath.isEmpty()) {
        resultDisplay->append("❌ 请先选择模型文件");
        convertButton->setEnabled(true);
        convertButton->setText("转换");
        return;
    }

    QString modelType = modelTypeCombo->currentText().trimmed();
    QString fileName = QFileInfo(modelPath).fileName();

    // 使用Linux原生工作目录（解决Windows文件系统权限问题）
    QString linuxWorkDir = QDir::homePath() + "/yolo_conversion";
    QString linuxModelsDir = linuxWorkDir + "/models";
    QString linuxResultDir = linuxWorkDir + "/result";

    // Windows输出目录（用户最终查看结果的地方）
    // 使用Linux路径格式，因为Qt应用在WSL2中运行
    QString windowsResultDir = "/mnt/e/code/autobit/yolo-rk3588/result";

    // 创建Linux工作目录
    QDir().mkpath(linuxModelsDir);
    QDir().mkpath(linuxResultDir);
    QDir().mkpath(windowsResultDir);

    // 复制模型文件到Linux工作目录（覆盖同名文件以节省空间）
    QString linuxModelPath = linuxModelsDir + "/" + fileName;
    if (QFile::exists(linuxModelPath)) {
        QFile::remove(linuxModelPath);
        resultDisplay->append("🔄 覆盖已存在的模型文件: " + fileName);
    }

    if (!QFile::copy(modelPath, linuxModelPath)) {
        resultDisplay->append("❌ 无法复制模型文件到Linux工作目录: " + linuxModelPath);
        convertButton->setEnabled(true);
        convertButton->setText("转换");
        return;
    }

    QString dockerModelPath = "/workspace/yolo-rk3588/models";
    QString dockerOutputPath = "result";

    // 构造 Docker 命令（使用Linux路径）
    QString dockerCommand = QString(
        "docker run --rm "
        "-v \"%1\":%2 "
        "-v \"%3\":/workspace/yolo-rk3588/result "
        "-w /workspace/yolo-rk3588 "
        "model-converter:latest "
        "--model %4 --weight %5 --output %6")
        .arg(linuxModelsDir)        // 使用Linux模型目录
        .arg(dockerModelPath)
        .arg(linuxResultDir)        // 使用Linux结果目录
        .arg(modelType)
        .arg(dockerModelPath + "/" + fileName)
        .arg(dockerOutputPath);

    // 启动转换进程
    process = new QProcess(this);
    connect(process, &QProcess::readyReadStandardOutput, this, [=]() {
        resultDisplay->append(QString::fromLocal8Bit(process->readAllStandardOutput()));
    });
    connect(process, &QProcess::readyReadStandardError, this, [=]() {
        resultDisplay->append(QString::fromLocal8Bit(process->readAllStandardError()));
    });
    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished), this, [=](int exitCode, QProcess::ExitStatus) {
        // 调用我们的处理函数
        onProcessFinished(exitCode);

        // 重置按钮状态
        convertButton->setEnabled(true);
        convertButton->setText("转换");
        process->deleteLater();
    });

    resultDisplay->append("✅ 模型文件已复制到Linux工作目录: " + linuxModelPath);
    resultDisplay->append("🚀 正在执行 Docker 转换命令...");
    resultDisplay->append(dockerCommand);

    process->start("bash", QStringList() << "-c" << dockerCommand);
}


void ConversionWindow::onProcessFinished(int exitCode)
{
    QString linuxResultDir = QDir::homePath() + "/yolo_conversion/result";
    // 使用Linux路径格式，因为Qt应用在WSL2中运行
    QString windowsResultDir = "/mnt/e/code/autobit/yolo-rk3588/result";

    if (exitCode == 0) {
        statusLabel->setText(QString::fromUtf8("✅ 转换成功，正在复制结果..."));
        resultDisplay->append(QString::fromUtf8("🔄 正在将转换结果复制到Windows目录..."));

        // 使用cp命令复制结果到Windows目录（直接覆盖）
        QString cpCommand = QString("cp -r %1/* %2/").arg(linuxResultDir).arg(windowsResultDir);
        resultDisplay->append(QString::fromUtf8("📋 执行复制命令: ") + cpCommand);

        // 确保目标目录存在
        QDir().mkpath(windowsResultDir);

        // 执行cp命令
        int cpResult = QProcess::execute("bash", QStringList() << "-c" << cpCommand);

        if (cpResult == 0) {
            statusLabel->setText(QString::fromUtf8("✅ 转换完成"));
            resultDisplay->append(QString::fromUtf8("✅ 转换结果已复制到: ") + windowsResultDir);
            resultDisplay->append(QString::fromUtf8("📁 Linux原始结果位置: ") + linuxResultDir);

            // 显示复制的文件信息
            QDir resultDir(windowsResultDir);
            QStringList entries = resultDir.entryList(QDir::AllEntries | QDir::NoDotAndDotDot, QDir::Time);
            resultDisplay->append(QString::fromUtf8("📋 复制的内容: ") + entries.join(", "));

            // 在WSL2中无法直接打开Windows文件管理器，提供替代方案
            resultDisplay->append(QString::fromUtf8("💡 在Windows中访问结果目录："));
            resultDisplay->append(QString::fromUtf8("   文件路径: E:\\code\\autobit\\yolo-rk3588\\result"));
            resultDisplay->append(QString::fromUtf8("   或在WSL2中: ") + windowsResultDir);
        } else {
            statusLabel->setText(QString::fromUtf8("⚠️ 转换成功，但复制失败"));
            resultDisplay->append(QString::fromUtf8("⚠️ cp命令执行失败，退出码: ") + QString::number(cpResult));
            resultDisplay->append(QString::fromUtf8("📁 请手动访问Linux结果: ") + linuxResultDir);
            resultDisplay->append(QString::fromUtf8("💡 Windows访问路径: \\\\wsl$\\Ubuntu-20.04") + linuxResultDir);
        }
    } else {
        statusLabel->setText(QString::fromUtf8("❌ 转换失败"));
        resultDisplay->append(QString::fromUtf8("❌ Docker转换过程失败，退出码: ") + QString::number(exitCode));
    }
}



void ConversionWindow::onProcessOutput()
{
    QByteArray output = process->readAllStandardOutput();
    resultDisplay->append(QString::fromLocal8Bit(output));
}

void ConversionWindow::onProcessErrorOutput()
{
    QByteArray errorOutput = process->readAllStandardError();
    resultDisplay->append(QString::fromLocal8Bit(errorOutput));
}

// 注意：现在使用cp命令进行复制，不再需要复杂的递归复制函数






