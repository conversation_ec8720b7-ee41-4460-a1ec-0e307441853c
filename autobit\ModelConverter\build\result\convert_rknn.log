I rknn-toolkit2 version: 2.3.2
--> Config model
done
--> Loading model

I Loading :   0%|                                                           | 0/123 [00:00<?, ?it/s]
I Loading : 100%|██████████████████████████████████████████████| 123/123 [00:00<00:00, 12359.83it/s]
done
--> Building model

I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   1%|▍                                               | 1/100 [00:00<00:00, 108.08it/s]
I OpFusing 0:   2%|▉                                               | 2/100 [00:00<00:00, 183.03it/s]
I OpFusing 0:   3%|█▍                                              | 3/100 [00:00<00:00, 258.92it/s]
I OpFusing 0:   4%|█▉                                              | 4/100 [00:00<00:00, 325.89it/s]
I OpFusing 0:   6%|██▉                                             | 6/100 [00:00<00:00, 299.84it/s]
I OpFusing 0:   8%|███▊                                            | 8/100 [00:00<00:00, 347.40it/s]
I OpFusing 0:   9%|████▎                                           | 9/100 [00:00<00:00, 380.09it/s]
I OpFusing 0:  10%|████▋                                          | 10/100 [00:00<00:00, 411.21it/s]
I OpFusing 0:  11%|█████▏                                         | 11/100 [00:00<00:00, 438.92it/s]
I OpFusing 0:  12%|█████▋                                         | 12/100 [00:00<00:00, 464.21it/s]
I OpFusing 0:  13%|██████                                         | 13/100 [00:00<00:00, 486.69it/s]
I OpFusing 0:  15%|███████                                        | 15/100 [00:00<00:00, 545.99it/s]
I OpFusing 0:  16%|███████▌                                       | 16/100 [00:00<00:00, 562.10it/s]
I OpFusing 0:  18%|████████▍                                      | 18/100 [00:00<00:00, 608.01it/s]
I OpFusing 0:  19%|████████▉                                      | 19/100 [00:00<00:00, 627.79it/s]
I OpFusing 0:  20%|█████████▍                                     | 20/100 [00:00<00:00, 641.97it/s]
I OpFusing 0:  21%|█████████▊                                     | 21/100 [00:00<00:00, 652.03it/s]
I OpFusing 0:  22%|██████████▎                                    | 22/100 [00:00<00:00, 665.63it/s]
I OpFusing 0:  24%|███████████▎                                   | 24/100 [00:00<00:00, 705.76it/s]
I OpFusing 0:  25%|███████████▊                                   | 25/100 [00:00<00:00, 713.35it/s]
I OpFusing 0:  27%|████████████▋                                  | 27/100 [00:00<00:00, 749.82it/s]
I OpFusing 0:  28%|█████████████▏                                 | 28/100 [00:00<00:00, 763.59it/s]
I OpFusing 0:  30%|██████████████                                 | 30/100 [00:00<00:00, 792.22it/s]
I OpFusing 0:  32%|███████████████                                | 32/100 [00:00<00:00, 807.44it/s]
I OpFusing 0:  33%|███████████████▌                               | 33/100 [00:00<00:00, 814.20it/s]
I OpFusing 0:  35%|████████████████▍                              | 35/100 [00:00<00:00, 844.33it/s]
I OpFusing 0:  36%|████████████████▉                              | 36/100 [00:00<00:00, 844.61it/s]
I OpFusing 0:  37%|█████████████████▍                             | 37/100 [00:00<00:00, 853.71it/s]
I OpFusing 0:  39%|██████████████████▎                            | 39/100 [00:00<00:00, 878.13it/s]
I OpFusing 0:  41%|███████████████████▎                           | 41/100 [00:00<00:00, 871.92it/s]
I OpFusing 0:  43%|████████████████████▏                          | 43/100 [00:00<00:00, 895.91it/s]
I OpFusing 0:  48%|██████████████████████▌                        | 48/100 [00:00<00:00, 921.02it/s]
I OpFusing 0:  50%|███████████████████████▌                       | 50/100 [00:00<00:00, 934.88it/s]
I OpFusing 0:  52%|████████████████████████▍                      | 52/100 [00:00<00:00, 948.53it/s]
I OpFusing 0:  54%|█████████████████████████▍                     | 54/100 [00:00<00:00, 970.74it/s]
I OpFusing 0:  55%|█████████████████████████▊                     | 55/100 [00:00<00:00, 978.13it/s]
I OpFusing 0:  57%|██████████████████████████▏                   | 57/100 [00:00<00:00, 1001.65it/s]
I OpFusing 0:  59%|███████████████████████████▏                  | 59/100 [00:00<00:00, 1022.27it/s]
I OpFusing 0:  60%|███████████████████████████▌                  | 60/100 [00:00<00:00, 1029.08it/s]
I OpFusing 0:  63%|████████████████████████████▉                 | 63/100 [00:00<00:00, 1063.58it/s]
I OpFusing 0:  65%|█████████████████████████████▉                | 65/100 [00:00<00:00, 1083.97it/s]
I OpFusing 0:  66%|██████████████████████████████▎               | 66/100 [00:00<00:00, 1086.47it/s]
I OpFusing 0:  68%|███████████████████████████████▎              | 68/100 [00:00<00:00, 1103.94it/s]
I OpFusing 0:  70%|████████████████████████████████▏             | 70/100 [00:00<00:00, 1119.65it/s]
I OpFusing 0:  72%|█████████████████████████████████             | 72/100 [00:00<00:00, 1141.48it/s]
I OpFusing 0:  74%|██████████████████████████████████            | 74/100 [00:00<00:00, 1159.00it/s]
I OpFusing 0:  76%|██████████████████████████████████▉           | 76/100 [00:00<00:00, 1179.20it/s]
I OpFusing 0:  78%|███████████████████████████████████▉          | 78/100 [00:00<00:00, 1199.81it/s]
I OpFusing 0:  80%|████████████████████████████████████▊         | 80/100 [00:00<00:00, 1216.37it/s]
I OpFusing 0:  82%|█████████████████████████████████████▋        | 82/100 [00:00<00:00, 1229.80it/s]
I OpFusing 0:  84%|██████████████████████████████████████▋       | 84/100 [00:00<00:00, 1244.58it/s]
I OpFusing 0:  86%|███████████████████████████████████████▌      | 86/100 [00:00<00:00, 1255.61it/s]
I OpFusing 0:  88%|████████████████████████████████████████▍     | 88/100 [00:00<00:00, 1269.82it/s]
I OpFusing 0:  90%|█████████████████████████████████████████▍    | 90/100 [00:00<00:00, 1287.30it/s]
I OpFusing 0:  92%|██████████████████████████████████████████▎   | 92/100 [00:00<00:00, 1299.49it/s]
I OpFusing 0:  95%|███████████████████████████████████████████▋  | 95/100 [00:00<00:00, 1320.62it/s]
I OpFusing 0: 100%|██████████████████████████████████████████████| 100/100 [00:00<00:00, 815.47it/s]
I OpFusing 1 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 1 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 639.67it/s]
I OpFusing 2 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 606.63it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 137.72it/s]
[1;33mW[0m [1;33mbuild: found outlier value, this may affect quantization accuracy
                        const nameabs_mean    abs_std     outlier value
                        344      0.83        1.39        14.282      [0m



I GraphPreparing :   0%|                                                    | 0/149 [00:00<?, ?it/s]
I GraphPreparing : 100%|████████████████████████████████████████| 149/149 [00:00<00:00, 9571.64it/s]

I Quantizating :   0%|                                                      | 0/149 [00:00<?, ?it/s]
I Quantizating :   1%|▎                                             | 1/149 [00:00<00:37,  3.98it/s]
I Quantizating :   1%|▌                                             | 2/149 [00:00<00:49,  2.96it/s]
I Quantizating :   2%|▉                                             | 3/149 [00:01<01:12,  2.02it/s]
I Quantizating :   3%|█▏                                            | 4/149 [00:01<01:01,  2.34it/s]
I Quantizating :   3%|█▌                                            | 5/149 [00:01<00:55,  2.58it/s]
I Quantizating :   5%|██▏                                           | 7/149 [00:02<00:34,  4.08it/s]
I Quantizating :   6%|██▊                                           | 9/149 [00:02<00:25,  5.50it/s]
I Quantizating :   7%|███                                          | 10/149 [00:02<00:24,  5.56it/s]
I Quantizating :   7%|███▎                                         | 11/149 [00:02<00:23,  5.76it/s]
I Quantizating :   9%|████▏                                        | 14/149 [00:02<00:16,  8.21it/s]
I Quantizating :  11%|████▊                                        | 16/149 [00:03<00:13,  9.81it/s]
I Quantizating :  12%|█████▍                                       | 18/149 [00:03<00:20,  6.26it/s]
I Quantizating :  13%|█████▋                                       | 19/149 [00:03<00:20,  6.28it/s]
I Quantizating :  14%|██████▎                                      | 21/149 [00:03<00:16,  7.94it/s]
I Quantizating :  15%|██████▉                                      | 23/149 [00:04<00:13,  9.67it/s]
I Quantizating :  17%|███████▌                                     | 25/149 [00:04<00:13,  9.35it/s]
I Quantizating :  19%|████████▍                                    | 28/149 [00:04<00:09, 12.98it/s]
I Quantizating :  20%|█████████                                    | 30/149 [00:04<00:10, 11.83it/s]
I Quantizating :  22%|█████████▉                                   | 33/149 [00:04<00:07, 15.24it/s]
I Quantizating :  24%|██████████▊                                  | 36/149 [00:04<00:07, 14.65it/s]
I Quantizating :  26%|███████████▍                                 | 38/149 [00:05<00:09, 11.28it/s]
I Quantizating :  28%|████████████▋                                | 42/149 [00:05<00:06, 16.00it/s]
I Quantizating :  30%|█████████████▌                               | 45/149 [00:05<00:06, 17.20it/s]
I Quantizating :  32%|██████████████▍                              | 48/149 [00:05<00:05, 17.82it/s]
I Quantizating :  36%|████████████████                             | 53/149 [00:05<00:04, 20.10it/s]
I Quantizating :  39%|█████████████████▌                           | 58/149 [00:05<00:03, 25.81it/s]
I Quantizating :  41%|██████████████████▍                          | 61/149 [00:06<00:04, 18.26it/s]
I Quantizating :  45%|████████████████████▏                        | 67/149 [00:06<00:03, 21.63it/s]
I Quantizating :  49%|██████████████████████                       | 73/149 [00:06<00:02, 28.03it/s]
I Quantizating :  55%|████████████████████████▊                    | 82/149 [00:06<00:01, 40.09it/s]
I Quantizating :  59%|██████████████████████████▌                  | 88/149 [00:06<00:01, 37.64it/s]
I Quantizating :  62%|████████████████████████████                 | 93/149 [00:06<00:01, 36.81it/s]
I Quantizating :  66%|█████████████████████████████▌               | 98/149 [00:07<00:01, 30.44it/s]
I Quantizating :  68%|██████████████████████████████              | 102/149 [00:07<00:01, 28.83it/s]
I Quantizating :  71%|███████████████████████████████▎            | 106/149 [00:07<00:01, 25.89it/s]
I Quantizating :  73%|████████████████████████████████▏           | 109/149 [00:07<00:01, 22.44it/s]
I Quantizating :  75%|█████████████████████████████████           | 112/149 [00:07<00:01, 20.85it/s]
I Quantizating :  77%|█████████████████████████████████▉          | 115/149 [00:08<00:01, 19.29it/s]
I Quantizating :  79%|██████████████████████████████████▊         | 118/149 [00:08<00:01, 19.09it/s]
I Quantizating :  82%|████████████████████████████████████        | 122/149 [00:08<00:01, 22.98it/s]
I Quantizating :  84%|████████████████████████████████████▉       | 125/149 [00:08<00:01, 21.57it/s]
I Quantizating :  87%|██████████████████████████████████████      | 129/149 [00:08<00:00, 23.00it/s]
I Quantizating :  89%|██████████████████████████████████████▉     | 132/149 [00:08<00:00, 22.92it/s]
I Quantizating :  92%|████████████████████████████████████████▍   | 137/149 [00:08<00:00, 24.78it/s]
I Quantizating :  95%|█████████████████████████████████████████▉  | 142/149 [00:09<00:00, 29.21it/s]
I Quantizating :  98%|███████████████████████████████████████████ | 146/149 [00:09<00:00, 29.69it/s]
I Quantizating : 100%|████████████████████████████████████████████| 149/149 [00:09<00:00, 15.71it/s]
[1;33mW[0m [1;33mbuild: The default input dtype of 'images' is changed from 'float32' to 'int8' in rknn model for performance!
                       Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of 'output0' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '340' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '342' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
I rknn building ...
I rknn building done.
done
--> Export rknn model
done
