I rknn-toolkit2 version: 2.3.2
--> Config model
done
--> Loading model

I Loading :   0%|                                                           | 0/123 [00:00<?, ?it/s]
I Loading : 100%|██████████████████████████████████████████████| 123/123 [00:00<00:00, 16722.83it/s]
done
--> Building model

I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   1%|▍                                               | 1/100 [00:00<00:00, 136.21it/s]
I OpFusing 0:   2%|▉                                               | 2/100 [00:00<00:00, 227.41it/s]
I OpFusing 0:   3%|█▍                                              | 3/100 [00:00<00:00, 312.95it/s]
I OpFusing 0:   4%|█▉                                              | 4/100 [00:00<00:00, 390.52it/s]
I OpFusing 0:   6%|██▉                                             | 6/100 [00:00<00:00, 441.61it/s]
I OpFusing 0:   8%|███▊                                            | 8/100 [00:00<00:00, 505.19it/s]
I OpFusing 0:   9%|████▎                                           | 9/100 [00:00<00:00, 547.45it/s]
I OpFusing 0:  10%|████▋                                          | 10/100 [00:00<00:00, 586.04it/s]
I OpFusing 0:  11%|█████▏                                         | 11/100 [00:00<00:00, 621.81it/s]
I OpFusing 0:  12%|█████▋                                         | 12/100 [00:00<00:00, 654.74it/s]
I OpFusing 0:  13%|██████                                         | 13/100 [00:00<00:00, 679.37it/s]
I OpFusing 0:  15%|███████                                        | 15/100 [00:00<00:00, 760.54it/s]
I OpFusing 0:  16%|███████▌                                       | 16/100 [00:00<00:00, 777.46it/s]
I OpFusing 0:  18%|████████▍                                      | 18/100 [00:00<00:00, 823.88it/s]
I OpFusing 0:  19%|████████▉                                      | 19/100 [00:00<00:00, 833.06it/s]
I OpFusing 0:  20%|█████████▍                                     | 20/100 [00:00<00:00, 846.44it/s]
I OpFusing 0:  21%|█████████▊                                     | 21/100 [00:00<00:00, 860.85it/s]
I OpFusing 0:  22%|██████████▎                                    | 22/100 [00:00<00:00, 878.74it/s]
I OpFusing 0:  24%|███████████▎                                   | 24/100 [00:00<00:00, 912.47it/s]
I OpFusing 0:  25%|███████████▊                                   | 25/100 [00:00<00:00, 922.95it/s]
I OpFusing 0:  27%|████████████▋                                  | 27/100 [00:00<00:00, 962.79it/s]
I OpFusing 0:  28%|█████████████▏                                 | 28/100 [00:00<00:00, 975.51it/s]
I OpFusing 0:  30%|█████████████▊                                | 30/100 [00:00<00:00, 1015.41it/s]
I OpFusing 0:  32%|██████████████▋                               | 32/100 [00:00<00:00, 1042.97it/s]
I OpFusing 0:  33%|███████████████▏                              | 33/100 [00:00<00:00, 1052.41it/s]
I OpFusing 0:  35%|████████████████                              | 35/100 [00:00<00:00, 1096.22it/s]
I OpFusing 0:  36%|████████████████▌                             | 36/100 [00:00<00:00, 1106.34it/s]
I OpFusing 0:  37%|█████████████████                             | 37/100 [00:00<00:00, 1117.03it/s]
I OpFusing 0:  39%|█████████████████▉                            | 39/100 [00:00<00:00, 1151.94it/s]
I OpFusing 0:  41%|██████████████████▊                           | 41/100 [00:00<00:00, 1142.22it/s]
I OpFusing 0:  43%|███████████████████▊                          | 43/100 [00:00<00:00, 1171.95it/s]
I OpFusing 0:  48%|██████████████████████                        | 48/100 [00:00<00:00, 1195.59it/s]
I OpFusing 0:  50%|███████████████████████                       | 50/100 [00:00<00:00, 1219.38it/s]
I OpFusing 0:  52%|███████████████████████▉                      | 52/100 [00:00<00:00, 1236.75it/s]
I OpFusing 0:  54%|████████████████████████▊                     | 54/100 [00:00<00:00, 1265.26it/s]
I OpFusing 0:  55%|█████████████████████████▎                    | 55/100 [00:00<00:00, 1270.75it/s]
I OpFusing 0:  57%|██████████████████████████▏                   | 57/100 [00:00<00:00, 1297.85it/s]
I OpFusing 0:  59%|███████████████████████████▏                  | 59/100 [00:00<00:00, 1320.49it/s]
I OpFusing 0:  60%|███████████████████████████▌                  | 60/100 [00:00<00:00, 1325.76it/s]
I OpFusing 0:  63%|████████████████████████████▉                 | 63/100 [00:00<00:00, 1367.24it/s]
I OpFusing 0:  65%|█████████████████████████████▉                | 65/100 [00:00<00:00, 1390.98it/s]
I OpFusing 0:  66%|██████████████████████████████▎               | 66/100 [00:00<00:00, 1394.98it/s]
I OpFusing 0:  68%|███████████████████████████████▎              | 68/100 [00:00<00:00, 1418.82it/s]
I OpFusing 0:  70%|████████████████████████████████▏             | 70/100 [00:00<00:00, 1438.17it/s]
I OpFusing 0:  72%|█████████████████████████████████             | 72/100 [00:00<00:00, 1461.62it/s]
I OpFusing 0:  74%|██████████████████████████████████            | 74/100 [00:00<00:00, 1475.52it/s]
I OpFusing 0:  76%|██████████████████████████████████▉           | 76/100 [00:00<00:00, 1498.18it/s]
I OpFusing 0:  78%|███████████████████████████████████▉          | 78/100 [00:00<00:00, 1521.02it/s]
I OpFusing 0:  80%|████████████████████████████████████▊         | 80/100 [00:00<00:00, 1541.86it/s]
I OpFusing 0:  82%|█████████████████████████████████████▋        | 82/100 [00:00<00:00, 1557.72it/s]
I OpFusing 0:  84%|██████████████████████████████████████▋       | 84/100 [00:00<00:00, 1578.22it/s]
I OpFusing 0:  86%|███████████████████████████████████████▌      | 86/100 [00:00<00:00, 1590.34it/s]
I OpFusing 0:  88%|████████████████████████████████████████▍     | 88/100 [00:00<00:00, 1610.50it/s]
I OpFusing 0:  90%|█████████████████████████████████████████▍    | 90/100 [00:00<00:00, 1629.64it/s]
I OpFusing 0:  92%|██████████████████████████████████████████▎   | 92/100 [00:00<00:00, 1643.30it/s]
I OpFusing 0:  95%|███████████████████████████████████████████▋  | 95/100 [00:00<00:00, 1665.87it/s]
I OpFusing 0: 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 1011.45it/s]
I OpFusing 1 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 1 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 753.10it/s]
I OpFusing 2 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 708.49it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 157.75it/s]
[1;33mW[0m [1;33mbuild: found outlier value, this may affect quantization accuracy
                        const nameabs_mean    abs_std     outlier value
                        344      0.83        1.39        14.282      [0m



I GraphPreparing :   0%|                                                    | 0/149 [00:00<?, ?it/s]
I GraphPreparing : 100%|████████████████████████████████████████| 149/149 [00:00<00:00, 9649.97it/s]

I Quantizating :   0%|                                                      | 0/149 [00:00<?, ?it/s]
I Quantizating :   1%|▎                                             | 1/149 [00:00<00:44,  3.34it/s]
I Quantizating :   1%|▌                                             | 2/149 [00:00<00:53,  2.73it/s]
I Quantizating :   2%|▉                                             | 3/149 [00:01<01:11,  2.05it/s]
I Quantizating :   3%|█▏                                            | 4/149 [00:01<01:05,  2.20it/s]
I Quantizating :   3%|█▌                                            | 5/149 [00:02<00:57,  2.49it/s]
I Quantizating :   5%|██▏                                           | 7/149 [00:02<00:35,  4.05it/s]
I Quantizating :   6%|██▊                                           | 9/149 [00:02<00:24,  5.64it/s]
I Quantizating :   7%|███                                          | 10/149 [00:02<00:24,  5.78it/s]
I Quantizating :   7%|███▎                                         | 11/149 [00:02<00:22,  6.19it/s]
I Quantizating :   9%|████▏                                        | 14/149 [00:02<00:15,  8.56it/s]
I Quantizating :  11%|████▊                                        | 16/149 [00:03<00:13,  9.99it/s]
I Quantizating :  12%|█████▍                                       | 18/149 [00:03<00:20,  6.54it/s]
I Quantizating :  13%|█████▋                                       | 19/149 [00:03<00:19,  6.65it/s]
I Quantizating :  14%|██████▎                                      | 21/149 [00:03<00:14,  8.56it/s]
I Quantizating :  16%|███████▏                                     | 24/149 [00:04<00:12, 10.22it/s]
I Quantizating :  19%|████████▍                                    | 28/149 [00:04<00:08, 13.73it/s]
I Quantizating :  20%|█████████                                    | 30/149 [00:04<00:09, 12.26it/s]
I Quantizating :  22%|█████████▉                                   | 33/149 [00:04<00:07, 15.05it/s]
I Quantizating :  24%|██████████▊                                  | 36/149 [00:04<00:08, 14.01it/s]
I Quantizating :  26%|███████████▍                                 | 38/149 [00:05<00:09, 11.39it/s]
I Quantizating :  28%|████████████▋                                | 42/149 [00:05<00:06, 15.89it/s]
I Quantizating :  30%|█████████████▌                               | 45/149 [00:05<00:06, 17.09it/s]
I Quantizating :  32%|██████████████▍                              | 48/149 [00:05<00:05, 17.07it/s]
I Quantizating :  36%|████████████████                             | 53/149 [00:05<00:04, 19.97it/s]
I Quantizating :  38%|█████████████████▏                           | 57/149 [00:05<00:03, 23.67it/s]
I Quantizating :  40%|██████████████████                           | 60/149 [00:05<00:03, 23.75it/s]
I Quantizating :  42%|███████████████████                          | 63/149 [00:06<00:04, 18.68it/s]
I Quantizating :  45%|████████████████████▏                        | 67/149 [00:06<00:03, 21.17it/s]
I Quantizating :  49%|██████████████████████                       | 73/149 [00:06<00:02, 28.64it/s]
I Quantizating :  54%|████████████████████████▏                    | 80/149 [00:06<00:01, 37.80it/s]
I Quantizating :  57%|█████████████████████████▋                   | 85/149 [00:06<00:01, 35.49it/s]
I Quantizating :  60%|███████████████████████████▏                 | 90/149 [00:06<00:01, 36.33it/s]
I Quantizating :  63%|████████████████████████████▍                | 94/149 [00:06<00:01, 30.99it/s]
I Quantizating :  66%|█████████████████████████████▌               | 98/149 [00:07<00:01, 30.62it/s]
I Quantizating :  68%|██████████████████████████████              | 102/149 [00:07<00:01, 28.41it/s]
I Quantizating :  71%|███████████████████████████████▎            | 106/149 [00:07<00:01, 25.33it/s]
I Quantizating :  73%|████████████████████████████████▏           | 109/149 [00:07<00:01, 21.73it/s]
I Quantizating :  75%|█████████████████████████████████           | 112/149 [00:07<00:01, 20.41it/s]
I Quantizating :  77%|█████████████████████████████████▉          | 115/149 [00:08<00:01, 19.15it/s]
I Quantizating :  79%|██████████████████████████████████▌         | 117/149 [00:08<00:01, 17.90it/s]
I Quantizating :  82%|████████████████████████████████████        | 122/149 [00:08<00:01, 23.93it/s]
I Quantizating :  84%|████████████████████████████████████▉       | 125/149 [00:08<00:01, 22.59it/s]
I Quantizating :  87%|██████████████████████████████████████      | 129/149 [00:08<00:00, 24.28it/s]
I Quantizating :  89%|██████████████████████████████████████▉     | 132/149 [00:08<00:00, 24.67it/s]
I Quantizating :  92%|████████████████████████████████████████▍   | 137/149 [00:08<00:00, 26.89it/s]
I Quantizating :  96%|██████████████████████████████████████████▏ | 143/149 [00:09<00:00, 32.46it/s]
I Quantizating :  99%|███████████████████████████████████████████▋| 148/149 [00:09<00:00, 28.69it/s]
I Quantizating : 100%|████████████████████████████████████████████| 149/149 [00:09<00:00, 16.05it/s]
[1;33mW[0m [1;33mbuild: The default input dtype of 'images' is changed from 'float32' to 'int8' in rknn model for performance!
                       Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of 'output0' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '340' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '342' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
I rknn building ...
I rknn building done.
done
--> Export rknn model
done
