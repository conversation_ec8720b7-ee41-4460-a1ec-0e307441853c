#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/workspace/yolo-rk3588/3rdparty/jpeg_turbo/include/turbojpeg.h

/workspace/yolo-rk3588/3rdparty/librga/include/drmrga.h
stdint.h
-
errno.h
-
sys/cdefs.h
-
rga.h
/workspace/yolo-rk3588/3rdparty/librga/include/rga.h
hardware/gralloc.h
-
hardware/hardware.h
-
system/graphics.h
-
cutils/native_handle.h
-
hardware/hardware_rockchip.h
-

/workspace/yolo-rk3588/3rdparty/librga/include/im2d.h
im2d_version.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_version.h
im2d_type.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_type.h
im2d_common.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_common.h
im2d_buffer.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_buffer.h
im2d_single.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_single.h
im2d_task.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_task.h
im2d_mpi.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_mpi.h

/workspace/yolo-rk3588/3rdparty/librga/include/im2d_buffer.h
im2d_type.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_type.h

/workspace/yolo-rk3588/3rdparty/librga/include/im2d_common.h
im2d_type.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_type.h

/workspace/yolo-rk3588/3rdparty/librga/include/im2d_mpi.h
im2d_type.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_type.h

/workspace/yolo-rk3588/3rdparty/librga/include/im2d_single.h
im2d_type.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_type.h

/workspace/yolo-rk3588/3rdparty/librga/include/im2d_task.h
im2d_type.h
/workspace/yolo-rk3588/3rdparty/librga/include/im2d_type.h

/workspace/yolo-rk3588/3rdparty/librga/include/im2d_type.h
stdint.h
-
rga.h
/workspace/yolo-rk3588/3rdparty/librga/include/rga.h

/workspace/yolo-rk3588/3rdparty/librga/include/im2d_version.h

/workspace/yolo-rk3588/3rdparty/librga/include/rga.h

/workspace/yolo-rk3588/3rdparty/stb_image/stb_image.h
stb_image.h
/workspace/yolo-rk3588/3rdparty/stb_image/stb_image.h
stdio.h
-
stdlib.h
-
stdarg.h
-
stddef.h
-
stdlib.h
-
string.h
-
limits.h
-
math.h
-
stdio.h
-
assert.h
-
stdint.h
-
emmintrin.h
-
intrin.h
-
arm_neon.h
-

/workspace/yolo-rk3588/3rdparty/stb_image/stb_image_write.h
stdlib.h
-
stdio.h
-
stdarg.h
-
stdlib.h
-
string.h
-
math.h
-
assert.h
-

/workspace/yolo-rk3588/utils/common.h

/workspace/yolo-rk3588/utils/file_utils.h

/workspace/yolo-rk3588/utils/image_utils.c
stdio.h
-
stdlib.h
-
dirent.h
-
math.h
-
sys/time.h
-
im2d.h
/workspace/yolo-rk3588/utils/im2d.h
drmrga.h
/workspace/yolo-rk3588/utils/drmrga.h
stb_image.h
/workspace/yolo-rk3588/utils/stb_image.h
stb_image_write.h
/workspace/yolo-rk3588/utils/stb_image_write.h
image_utils.h
/workspace/yolo-rk3588/utils/image_utils.h
file_utils.h
/workspace/yolo-rk3588/utils/file_utils.h
turbojpeg.h
/workspace/yolo-rk3588/utils/turbojpeg.h

/workspace/yolo-rk3588/utils/image_utils.h
common.h
/workspace/yolo-rk3588/utils/common.h

