===================================
BUILD_DEMO_NAME:   yolov8
BUILD_DEMO_PATH:   /workspace/yolo-rk3588/yolov8/cpp
TARGET_SOC:        rk3588
TARGET_ARCH:       aarch64
BUILD_TYPE:        Release
ENABLE_ASAN:       OFF
DISABLE_RGA:       OFF
DISABLE_LIBJPEG:   OFF
CC:                /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc
CXX:               /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-g++
INPUT_PATH:        /workspace/yolo-rk3588/result/yolov8
OUTPUT_PATH:       /workspace/yolo-rk3588/result/yolov8/deploy
INSTALL_DIR:       /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo
BUILD_DIR:         /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release
===================================
-- !!!!!!!!!!!CMAKE_SYSTEM_NAME: Linux
-- Configuring done
-- Generating done
-- Build files have been written to: /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release
[ 25%] Built target fileutils
[ 25%] Built target imagedrawing
[ 37%] Built target audioutils
[ 50%] Built target imageutils
[100%] Built target rknn_yolov8_demo_zero_copy
[100%] Built target rknn_yolov8_demo
[ 12%] Built target fileutils
[ 25%] Built target imageutils
[ 37%] Built target imagedrawing
[ 62%] Built target rknn_yolov8_demo_zero_copy
[ 87%] Built target rknn_yolov8_demo
[100%] Built target audioutils
Install the project...
-- Install configuration: "Release"
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/./rknn_yolov8_demo_zero_copy
-- Set runtime path of "/workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/./rknn_yolov8_demo_zero_copy" to "$ORIGIN/../lib"
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/./rknn_yolov8_demo
-- Set runtime path of "/workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/./rknn_yolov8_demo" to "$ORIGIN/../lib"
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/model/bus.jpg
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/model/coco_80_labels_list.txt
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/model/yolov8.rknn
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/lib/librknnrt.so
-- Installing: /workspace/yolo-rk3588/result/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo/lib/librga.so
