#!/bin/bash
# AutoBit 激活系统测试脚本
# 用于验证双模式激活系统功能

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo_info "=== AutoBit 激活系统测试 ==="
echo_info ""

# 进入项目目录
cd "$(dirname "$0")/ModelConverter"

# 检查编译结果
if [ ! -f "build/ModelConverter" ]; then
    echo_error "可执行文件不存在，请先运行 build_and_run.sh 进行编译"
    exit 1
fi

echo_success "找到可执行文件: build/ModelConverter"

# 检查依赖库
if [ ! -f "build/libsf-core-ls.so" ]; then
    echo_warning "许可证库不存在，复制库文件..."
    cp libsf-core-ls.so build/
fi

# 设置环境变量
export LD_LIBRARY_PATH="$(pwd)/build:$LD_LIBRARY_PATH"

# 检查Qt环境
QT_DIR="/opt/Qt5.12.6/5.12.6/gcc_64"
if [ -d "$QT_DIR" ]; then
    export PATH="$QT_DIR/bin:$PATH"
    export LD_LIBRARY_PATH="$QT_DIR/lib:$LD_LIBRARY_PATH"
    export QT_QPA_PLATFORM_PLUGIN_PATH="$QT_DIR/lib/plugins"
    echo_success "Qt环境配置完成"
else
    echo_warning "Qt目录不存在，使用系统Qt"
fi

# 设置字体环境
export QT_QPA_FONTDIR="/usr/share/fonts"
export FONTCONFIG_PATH="/etc/fonts"

# 检查X11环境
if [ -z "$DISPLAY" ]; then
    echo_warning "DISPLAY未设置，尝试自动配置..."
    export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
    echo_info "DISPLAY设置为: $DISPLAY"
fi

echo_info ""
echo_info "=== 测试硬件指纹功能 ==="

# 创建简单的测试程序来验证硬件指纹
cat > test_fingerprint.cpp << 'EOF'
#include "HardwareFingerprint.h"
#include <QCoreApplication>
#include <QDebug>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 硬件指纹测试 ===";
    
    QString fingerprint = HardwareFingerprint::getDeviceFingerprint();
    qDebug() << "设备指纹:" << fingerprint;
    
    QString cpu = HardwareFingerprint::getCpuInfo();
    qDebug() << "CPU信息:" << cpu;
    
    QString mac = HardwareFingerprint::getMacAddress();
    qDebug() << "MAC地址:" << mac;
    
    QString system = HardwareFingerprint::getSystemInfo();
    qDebug() << "系统信息:" << system;
    
    // 验证指纹一致性
    QString fingerprint2 = HardwareFingerprint::getDeviceFingerprint();
    if (fingerprint == fingerprint2) {
        qDebug() << "✅ 指纹一致性测试通过";
    } else {
        qDebug() << "❌ 指纹一致性测试失败";
    }
    
    return 0;
}
EOF

echo_info "编译硬件指纹测试程序..."
cd build
if [ -d "$QT_DIR" ]; then
    "$QT_DIR/bin/qmake" -project ../test_fingerprint.cpp
    "$QT_DIR/bin/qmake" test_fingerprint.pro
else
    qmake -project ../test_fingerprint.cpp
    qmake test_fingerprint.pro
fi

make test_fingerprint 2>/dev/null || echo_warning "硬件指纹测试编译失败（可能缺少依赖）"

if [ -f "test_fingerprint" ]; then
    echo_info "运行硬件指纹测试..."
    ./test_fingerprint
    echo_success "硬件指纹测试完成"
else
    echo_warning "跳过硬件指纹测试（编译失败）"
fi

cd ..

echo_info ""
echo_info "=== 测试许可证管理功能 ==="

# 检查许可证配置目录
CONFIG_DIR="$HOME/.config/AutoBit"
if [ -d "$CONFIG_DIR" ]; then
    echo_info "许可证配置目录存在: $CONFIG_DIR"
    if [ -f "$CONFIG_DIR/license.conf" ]; then
        echo_info "发现现有许可证文件"
        echo_warning "建议清除测试: rm -f $CONFIG_DIR/license.conf"
    fi
else
    echo_info "许可证配置目录不存在，将在首次运行时创建"
fi

echo_info ""
echo_info "=== 激活码格式测试 ==="

# 测试激活码格式
TEST_CODES=(
    "TEST-1234-5678-ABCD"  # 有效格式
    "DEBUG-2024-0629-DEMO" # 有效格式
    "E76G-JEQR-EQRA-T7ZW"  # 有效格式
    "invalid-code"         # 无效格式
    "1234-5678-9012"       # 无效格式（长度不够）
    "ABCD-EFGH-IJKL-MNOP-QRST" # 无效格式（太长）
)

for code in "${TEST_CODES[@]}"; do
    if [[ $code =~ ^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$ ]]; then
        echo_success "✅ $code - 格式有效"
    else
        echo_warning "❌ $code - 格式无效"
    fi
done

echo_info ""
echo_info "=== 依赖检查 ==="

# 检查程序依赖
echo_info "检查可执行文件依赖..."
cd build
ldd ./ModelConverter | head -20

echo_info ""
echo_info "=== 启动测试 ==="

echo_info "测试程序启动（5秒后自动退出）..."
echo_info "如果看到GUI界面，说明程序启动成功"
echo_info ""

# 启动程序并在5秒后自动退出
timeout 5s ./ModelConverter &
PID=$!

sleep 2
if ps -p $PID > /dev/null; then
    echo_success "✅ 程序启动成功"
    kill $PID 2>/dev/null || true
else
    echo_warning "⚠️ 程序可能启动失败或立即退出"
fi

echo_info ""
echo_info "=== 测试总结 ==="
echo_success "✅ 项目编译成功"
echo_success "✅ 依赖库检查完成"
echo_success "✅ 环境配置完成"
echo_success "✅ 激活码格式验证完成"

echo_info ""
echo_info "=== 手动测试指南 ==="
echo_info "1. 运行完整程序:"
echo_info "   cd build && ./ModelConverter"
echo_info ""
echo_info "2. 调试模式测试:"
echo_info "   - 勾选'调试模式'复选框"
echo_info "   - 输入测试激活码: TEST-1234-5678-ABCD"
echo_info "   - 点击激活按钮"
echo_info ""
echo_info "3. 生产模式测试:"
echo_info "   - 取消勾选'调试模式'"
echo_info "   - 输入真实激活码"
echo_info "   - 点击激活按钮（需要网络连接）"
echo_info ""
echo_info "4. 模型转换测试:"
echo_info "   - 激活成功后进入转换界面"
echo_info "   - 选择模型类型和文件"
echo_info "   - 点击开始转换"
echo_info ""

echo_success "测试脚本执行完成！"
