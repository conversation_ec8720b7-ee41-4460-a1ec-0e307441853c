# AutoBit 激活系统技术文档

## 🔐 激活系统概述

AutoBit 激活系统是一个基于硬件指纹的双模式许可证管理系统，旨在为模型转换工具提供安全可靠的授权机制。

## 🏗️ 系统架构

### 核心组件

1. **HardwareFingerprint**: 硬件指纹生成器
2. **LicenseManager**: 许可证管理器
3. **ActivationWindow**: 激活界面
4. **加密存储模块**: 本地许可证安全存储

### 数据流图

```
用户输入激活码 → 激活模式判断 → 硬件指纹生成 → 许可证验证 → 加密存储 → 激活完成
```

## 🔍 硬件指纹技术

### 指纹组成要素

| 组件 | 获取方法 | 权重 | 备注 |
|------|----------|------|------|
| CPU信息 | /proc/cpuinfo | 高 | 处理器型号和特征 |
| 主板信息 | dmidecode | 高 | 主板序列号 |
| MAC地址 | QNetworkInterface | 中 | 第一个有效网卡 |
| 硬盘序列号 | lsblk/udevadm | 中 | 系统盘序列号 |
| 系统信息 | QSysInfo | 低 | 内核版本和架构 |

### 指纹生成算法

```cpp
QString HardwareFingerprint::getDeviceFingerprint()
{
    QStringList components;
    
    // 收集硬件信息
    components << getCpuInfo();
    components << getMotherboardInfo();
    components << getMacAddress();
    components << getDiskSerial();
    components << getSystemInfo();
    
    // 清理和组合
    components.removeAll("");
    QString combined = components.join("|");
    
    // 生成MD5哈希
    return generateMD5(combined);
}
```

### 信息清理规则

- 移除空白字符和特殊字符
- 过滤无效值（"Not Specified", "Unknown"等）
- 统一字符格式（大小写、编码）
- 保留字母数字和连字符

## 🔒 许可证管理

### 许可证数据结构

```json
{
  "activation_code": "XXXX-XXXX-XXXX-XXXX",
  "device_id": "32位MD5硬件指纹",
  "product_id": "40201",
  "activated_at": "2024-06-29T10:00:00Z",
  "expiry": "2025-06-29T10:00:00Z",
  "status": "active",
  "debug_mode": false
}
```

### 加密存储机制

#### 加密流程
1. **序列化**: JSON → 字符串
2. **加密**: XOR加密（可扩展AES）
3. **编码**: Base64编码
4. **存储**: 写入配置文件

#### 解密流程
1. **读取**: 从配置文件读取
2. **解码**: Base64解码
3. **解密**: XOR解密
4. **反序列化**: 字符串 → JSON

#### 密钥生成
```cpp
QString LicenseManager::generateEncryptionKey(const QString &deviceId)
{
    QString salt = "AutobitModelConverter2024";
    return generateMD5(deviceId + salt);
}
```

### 完整性验证

- **校验和**: MD5(许可证数据 + 设备ID)
- **时间戳验证**: 检查激活时间和过期时间
- **硬件绑定**: 验证当前设备指纹与存储指纹一致

## 🔄 双模式设计

### 生产模式（Production Mode）

#### 特点
- 硬件严格绑定
- 在线服务器验证
- 加密许可证存储
- 防篡改检测

#### 激活流程
```
输入激活码 → 生成设备指纹 → 发送服务器验证 → 接收许可证 → 加密存储 → 激活成功
```

#### 服务器通信
```cpp
// 请求数据
{
  "activation_code": "用户输入的激活码",
  "device_id": "当前设备硬件指纹",
  "product_id": "40201",
  "timestamp": "当前时间戳"
}

// 响应数据
{
  "status": "success|error",
  "message": "响应消息",
  "license": {许可证数据}
}
```

### 调试模式（Debug Mode）

#### 特点
- 离线快速验证
- 预设测试激活码
- 简化验证流程
- 开发友好

#### 预设激活码
- `TEST-1234-5678-ABCD`
- `DEBUG-2024-0629-DEMO`
- `DEV-AUTOBIT-MODEL-CONV`
- `E76G-JEQR-EQRA-T7ZW`

#### 激活流程
```
输入激活码 → 检查预设列表 → 生成调试许可证 → 本地存储 → 激活成功
```

## 🛡️ 安全机制

### 防护措施

1. **硬件绑定**: 防止许可证在不同设备间转移
2. **加密存储**: 防止本地许可证被篡改
3. **完整性校验**: 检测许可证数据完整性
4. **时间验证**: 防止过期许可证使用
5. **格式验证**: 确保激活码格式正确

### 安全风险评估

| 风险类型 | 风险等级 | 防护措施 | 备注 |
|----------|----------|----------|------|
| 许可证复制 | 高 | 硬件指纹绑定 | 有效防护 |
| 本地篡改 | 中 | 加密+校验和 | 基本防护 |
| 网络攻击 | 中 | HTTPS通信 | 需要实现 |
| 逆向工程 | 低 | 代码混淆 | 可选实现 |

### 安全建议

1. **生产环境**:
   - 使用HTTPS进行服务器通信
   - 实施代码混淆和反调试
   - 定期更新加密算法
   - 监控异常激活行为

2. **开发环境**:
   - 限制调试模式使用范围
   - 记录调试激活日志
   - 定期清理测试许可证

## 🔧 技术实现细节

### 关键类设计

#### HardwareFingerprint类
```cpp
class HardwareFingerprint
{
public:
    static QString getDeviceFingerprint();
    static QString getCpuInfo();
    static QString getMotherboardInfo();
    static QString getMacAddress();
    static QString getDiskSerial();
    static QString getSystemInfo();
    static bool verifyFingerprint(const QString &stored, const QString &current);
    
private:
    static QString executeCommand(const QString &command);
    static QString cleanString(const QString &input);
    static QString generateMD5(const QString &input);
};
```

#### LicenseManager类
```cpp
class LicenseManager : public QObject
{
    Q_OBJECT
    
public:
    enum ActivationResult { Success, InvalidCode, NetworkError, HardwareMismatch, ... };
    enum LicenseStatus { NotActivated, Valid, Expired, Invalid };
    
    void setDebugMode(bool enabled);
    LicenseStatus checkLicenseStatus();
    void activateLicense(const QString &code, const QString &deviceId = QString());
    QString getCurrentDeviceId();
    
signals:
    void activationFinished(ActivationResult result, const QString &message);
    void licenseStatusChanged(bool isValid);
    
private:
    void activateProduction(const QString &code, const QString &deviceId);
    void activateDebug(const QString &code);
    void saveLicense(const QJsonObject &data);
    QJsonObject loadLicense();
    QString encryptData(const QString &data, const QString &key);
    QString decryptData(const QString &encrypted, const QString &key);
};
```

### 错误处理机制

#### 错误类型定义
```cpp
enum ActivationResult {
    Success,                // 激活成功
    InvalidCode,           // 激活码无效
    NetworkError,          // 网络错误
    HardwareMismatch,      // 硬件不匹配
    ServerError,           // 服务器错误
    AlreadyActivated,      // 已经激活
    ExpiredLicense,        // 许可证过期
    DebugModeSuccess       // 调试模式激活成功
};
```

#### 错误处理策略
- **网络错误**: 提供离线模式选项
- **硬件不匹配**: 提供重新激活选项
- **服务器错误**: 显示详细错误信息
- **格式错误**: 实时输入验证

## 📊 性能优化

### 优化策略

1. **硬件信息缓存**: 避免重复获取硬件信息
2. **异步网络请求**: 防止UI阻塞
3. **延迟加载**: 按需加载许可证信息
4. **内存管理**: 及时释放网络请求对象

### 性能指标

- **硬件指纹生成**: < 100ms
- **许可证验证**: < 50ms
- **网络激活**: < 5s
- **UI响应**: < 16ms

## 🧪 测试策略

### 测试用例

1. **硬件指纹测试**:
   - 同一设备多次生成指纹一致性
   - 不同设备指纹唯一性
   - 硬件变更后指纹变化

2. **许可证管理测试**:
   - 加密解密正确性
   - 完整性校验有效性
   - 过期时间验证

3. **激活流程测试**:
   - 有效激活码激活成功
   - 无效激活码激活失败
   - 网络异常处理
   - 调试模式功能

### 测试环境

- **单元测试**: Qt Test框架
- **集成测试**: 模拟服务器环境
- **压力测试**: 并发激活请求
- **兼容性测试**: 多平台验证

## 📈 监控和日志

### 日志记录

```cpp
qDebug() << "[硬件指纹] 生成指纹:" << fingerprint;
qDebug() << "[许可证管理器] 激活结果:" << result;
qDebug() << "[激活窗口] 状态更新:" << status;
```

### 监控指标

- 激活成功率
- 硬件指纹冲突率
- 网络请求延迟
- 错误类型分布

## 🔮 未来扩展

### 计划功能

1. **高级加密**: 实现AES-256加密
2. **云端同步**: 许可证云端备份
3. **多设备管理**: 支持一码多设备
4. **使用统计**: 功能使用情况统计
5. **自动更新**: 许可证自动续期

### 架构演进

- 微服务化许可证服务
- 区块链技术应用
- 机器学习异常检测
- 零信任安全模型

---

© 2024 AutoBit Team. All rights reserved.
