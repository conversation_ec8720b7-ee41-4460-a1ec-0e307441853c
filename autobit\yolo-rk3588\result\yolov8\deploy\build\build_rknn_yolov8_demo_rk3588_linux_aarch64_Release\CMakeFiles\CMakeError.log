Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_85d37/fast && /usr/bin/make -f CMakeFiles/cmTC_85d37.dir/build.make CMakeFiles/cmTC_85d37.dir/build
make[1]: Entering directory '/workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_85d37.dir/src.c.o
/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_85d37.dir/src.c.o   -c /workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_85d37
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_85d37.dir/link.txt --verbose=1
/workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc  -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_85d37.dir/src.c.o  -o cmTC_85d37 
CMakeFiles/cmTC_85d37.dir/src.c.o: In function `main':
src.c:(.text+0x34): undefined reference to `pthread_create'
src.c:(.text+0x3c): undefined reference to `pthread_detach'
src.c:(.text+0x48): undefined reference to `pthread_join'
src.c:(.text+0x58): undefined reference to `pthread_atfork'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_85d37.dir/build.make:87: cmTC_85d37] Error 1
make[1]: Leaving directory '/workspace/yolo-rk3588/result/yolov8/deploy/build/build_rknn_yolov8_demo_rk3588_linux_aarch64_Release/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_85d37/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

