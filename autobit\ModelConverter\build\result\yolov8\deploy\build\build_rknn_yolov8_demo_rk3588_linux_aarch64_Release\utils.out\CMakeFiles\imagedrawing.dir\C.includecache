#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/workspace/yolo-rk3588/utils/common.h

/workspace/yolo-rk3588/utils/font.h

/workspace/yolo-rk3588/utils/image_drawing.c
stdio.h
-
stdlib.h
-
string.h
-
ctype.h
-
image_drawing.h
/workspace/yolo-rk3588/utils/image_drawing.h
font.h
/workspace/yolo-rk3588/utils/font.h

/workspace/yolo-rk3588/utils/image_drawing.h
common.h
/workspace/yolo-rk3588/utils/common.h

