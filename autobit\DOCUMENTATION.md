# AutoBit 模型转换工具 v2.0 - 完整文档

## 📋 项目概述

AutoBit 模型转换工具是一个专业的YOLO模型转换应用，支持将PyTorch和ONNX格式的YOLOv5/v8模型转换为RK3588平台优化的RKNN格式。

### ✨ 主要特性

- 🔐 **双模式激活系统**: 支持生产模式（硬件绑定）和调试模式（离线验证）
- 🎨 **现代化UI设计**: 采用现代化界面设计，提供优秀的用户体验
- 🔒 **硬件指纹绑定**: 基于多种硬件信息生成唯一设备标识
- 🛡️ **加密许可证存储**: 本地许可证采用加密存储，确保安全性
- 🚀 **Docker集成转换**: 集成Docker环境，确保转换过程的一致性
- 📊 **实时转换监控**: 提供详细的转换日志和进度监控

### 🏗️ 系统架构

```
AutoBit/
├── ModelConverter/           # Qt应用程序
│   ├── main.cpp             # 程序入口
│   ├── activationwindow.*   # 激活窗口（现代化UI）
│   ├── conversionwindow.*   # 转换窗口（现代化UI）
│   ├── HardwareFingerprint.*# 硬件指纹生成器
│   ├── LicenseManager.*     # 许可证管理器
│   └── ModelConverter.pro  # Qt项目配置
├── build_and_run.sh         # 编译启动脚本
└── README.md               # 本文档
```

## 🔐 激活系统详解

### 双模式设计

#### 1. 生产模式（Production Mode）
- **硬件绑定**: 基于CPU、主板、MAC地址、硬盘序列号等生成设备指纹
- **在线验证**: 连接激活服务器进行实时验证
- **加密存储**: 许可证本地加密存储，防止篡改
- **安全性高**: 适用于正式部署环境

#### 2. 调试模式（Debug Mode）
- **离线验证**: 无需网络连接，使用预设激活码
- **快速激活**: 简化验证流程，便于开发调试
- **测试友好**: 支持多个测试激活码
- **开发便利**: 适用于开发和测试环境

### 硬件指纹算法

硬件指纹基于以下信息生成：

1. **CPU信息**: 处理器型号和序列号
2. **主板信息**: 主板序列号和产品名称
3. **网卡MAC**: 第一个有效网卡的MAC地址
4. **硬盘序列号**: 系统盘序列号
5. **系统信息**: 内核类型、版本和架构

**生成流程**:
```
硬件信息收集 → 信息清理 → 组合拼接 → MD5哈希 → 32位指纹
```

### 许可证管理

#### 许可证结构
```json
{
  "activation_code": "XXXX-XXXX-XXXX-XXXX",
  "device_id": "硬件指纹",
  "product_id": "40201",
  "activated_at": "2024-06-29T10:00:00Z",
  "expiry": "2025-06-29T10:00:00Z",
  "status": "active"
}
```

#### 加密存储
- **加密算法**: XOR加密（可扩展为AES）
- **密钥生成**: 基于设备指纹生成加密密钥
- **完整性校验**: MD5校验和验证许可证完整性
- **存储位置**: `~/.config/AutoBit/license.conf`

## 🚀 快速开始

### 环境要求

- **操作系统**: Linux (推荐Ubuntu 20.04)
- **Qt版本**: Qt 5.12.6
- **Docker**: Docker Desktop
- **编译器**: GCC 7.0+
- **显示**: X11支持（WSL2需要VcXsrv）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd autobit
```

2. **设置权限**
```bash
chmod +x build_and_run.sh
```

3. **运行脚本**
```bash
./build_and_run.sh
```

### WSL2环境配置

如果在WSL2环境中运行，需要：

1. **启动X11服务器**
   - 下载并安装VcXsrv
   - 启动XLaunch，选择"Disable access control"

2. **进入WSL2环境**
```bash
wsl -d Ubuntu-20.04
```

3. **设置显示变量**（脚本会自动设置）
```bash
export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
```

## 💻 使用指南

### 激活流程

#### 生产模式激活
1. 启动应用程序
2. 输入有效激活码（格式：XXXX-XXXX-XXXX-XXXX）
3. 点击"激活"按钮
4. 等待在线验证完成
5. 激活成功后进入主界面

#### 调试模式激活
1. 启动应用程序
2. 勾选"调试模式"复选框
3. 输入测试激活码：
   - `TEST-1234-5678-ABCD`
   - `DEBUG-2024-0629-DEMO`
   - `DEV-AUTOBIT-MODEL-CONV`
   - `E76G-JEQR-EQRA-T7ZW`
4. 点击"激活"按钮
5. 立即激活成功

### 模型转换

1. **选择模型类型**: YOLOv5 或 YOLOv8
2. **选择模型文件**: 支持 .pt 和 .onnx 格式
3. **开始转换**: 点击"开始转换"按钮
4. **监控进度**: 查看实时转换日志
5. **获取结果**: 转换完成后自动打开结果目录

### 界面功能

#### 激活窗口
- 🔐 激活码输入和验证
- 🔧 调试模式切换
- 📱 设备信息显示
- 📊 激活状态反馈

#### 转换窗口
- 📁 模型文件选择
- ⚙️ 转换参数配置
- 📋 实时日志显示
- 📊 转换进度监控
- 📂 结果文件管理

## 🔧 开发指南

### 项目结构

```
ModelConverter/
├── 核心组件/
│   ├── HardwareFingerprint  # 硬件指纹生成
│   ├── LicenseManager       # 许可证管理
│   ├── ActivationWindow     # 激活界面
│   └── ConversionWindow     # 转换界面
├── 配置文件/
│   ├── ModelConverter.pro   # Qt项目配置
│   └── ModelConverter.qrc   # 资源文件
└── 依赖库/
    ├── libsf-core-ls.so     # 许可证库
    └── SFCoreIntf.h         # 接口头文件
```

### 编译配置

Qt项目配置要点：
- Qt路径：`/opt/Qt5.12.6/5.12.6/gcc_64`
- 网络支持：`QT += network`
- C++标准：`CONFIG += c++17`
- 条件编译：支持DEBUG/RELEASE模式

### 扩展开发

#### 添加新的硬件指纹源
```cpp
// 在HardwareFingerprint.cpp中添加
QString HardwareFingerprint::getNewHardwareInfo()
{
    // 实现新的硬件信息获取逻辑
    return cleanString(info);
}
```

#### 自定义加密算法
```cpp
// 在LicenseManager.cpp中替换
QString LicenseManager::encryptData(const QString &data, const QString &key)
{
    // 实现更强的加密算法（如AES）
    return encryptedData;
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. Qt环境问题
**问题**: qmake命令未找到
**解决**: 确保Qt路径正确设置
```bash
export PATH="/opt/Qt5.12.6/5.12.6/gcc_64/bin:$PATH"
```

#### 2. X11显示问题
**问题**: GUI无法显示
**解决**: 检查DISPLAY变量和X11服务器
```bash
echo $DISPLAY
xeyes  # 测试X11连接
```

#### 3. Docker权限问题
**问题**: Docker命令权限不足
**解决**: 添加用户到docker组
```bash
sudo usermod -aG docker $USER
```

#### 4. 许可证验证失败
**问题**: 硬件指纹不匹配
**解决**: 
- 检查硬件信息是否发生变化
- 使用调试模式进行测试
- 重新生成许可证

### 调试技巧

#### 启用详细日志
```bash
export QT_LOGGING_RULES="*.debug=true"
./ModelConverter
```

#### 查看硬件指纹
```cpp
qDebug() << HardwareFingerprint::getDeviceFingerprint();
```

#### 许可证状态检查
```cpp
LicenseManager manager;
qDebug() << "Status:" << manager.checkLicenseStatus();
```

## 📞 技术支持

### 联系方式
- **项目地址**: [GitHub Repository]
- **技术支持**: <EMAIL>
- **文档更新**: 2024-06-29

### 版本历史
- **v2.0.0**: 双模式激活系统 + 现代化UI
- **v1.0.0**: 基础模型转换功能

---

© 2024 AutoBit Team. All rights reserved.
