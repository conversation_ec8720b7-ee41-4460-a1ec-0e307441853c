#ifndef CONVERSIONWINDOW_H
#define CONVERSIONWINDOW_H

#include <QWidget>
#include <QProcess>
#include <QComboBox>
#include <QLineEdit>
#include <QPushButton>
#include <QTextEdit>
#include <QLabel>
#include <QProgressBar>
#include <QFrame>
#include <QGroupBox>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QTabWidget>
#include <QListWidget>
#include <QPropertyAnimation>
#include <QTimer>
#include <QMovie>
#include <QScrollArea>

class ConversionWindow : public QWidget
{
    Q_OBJECT

public:
    explicit ConversionWindow(QWidget *parent = nullptr);

private slots:
    void onBrowseClicked();
    void onConvertClicked();
    void onProcessOutput();
    void onProcessErrorOutput();
    void onProcessFinished(int exitCode);
    void onClearLogClicked();
    void onOpenResultClicked();

private:
    void setupUI();
    void setupStyles();
    void setupConnections();
    void updateProgress(int value);
    void setConversionStatus(const QString &status, const QString &type = "info");
    void addLogMessage(const QString &message, const QString &type = "info");
    void resetUI();

    // UI组件
    QFrame *headerFrame;
    QFrame *inputFrame;
    QFrame *outputFrame;

    QLabel *titleLabel;
    QLabel *statusIconLabel;

    QGroupBox *modelGroup;
    QComboBox *modelTypeCombo;
    QLineEdit *modelPathEdit;
    QPushButton *browseButton;

    QGroupBox *actionGroup;
    QPushButton *convertButton;
    QPushButton *clearLogButton;
    QPushButton *openResultButton;

    QProgressBar *progressBar;
    QLabel *progressLabel;
    QLabel *statusLabel;

    QTabWidget *outputTabs;
    QTextEdit *logDisplay;
    QTextEdit *errorDisplay;
    QListWidget *resultList;

    // 动画和效果
    QPropertyAnimation *progressAnimation;
    QTimer *statusTimer;
    QMovie *loadingMovie;

    QProcess *process;
    bool isConverting;
};

#endif