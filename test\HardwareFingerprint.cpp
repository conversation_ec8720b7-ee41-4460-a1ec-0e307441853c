#include "HardwareFingerprint.h"
#include <QProcess>
#include <QRegularExpression>
#include <QStandardPaths>

QString HardwareFingerprint::getDeviceFingerprint()
{
    QStringList components;
    
    // 收集各种硬件信息
    components << getCpuInfo();
    components << getMotherboardInfo();
    components << getMacAddress();
    components << getDiskSerial();
    components << getSystemInfo();
    
    // 移除空值
    components.removeAll("");
    
    // 组合所有信息
    QString combined = components.join("|");
    
    qDebug() << "[硬件指纹] 组合信息:" << combined;
    
    // 生成MD5哈希
    QString fingerprint = generateMD5(combined);
    
    qDebug() << "[硬件指纹] 生成指纹:" << fingerprint;
    
    return fingerprint;
}

QString HardwareFingerprint::getCpuInfo()
{
    QString cpuInfo;
    
    // Linux ARM64: 从/proc/cpuinfo获取CPU信息
    cpuInfo = executeCommand("cat /proc/cpuinfo | grep 'model name' | head -1 | cut -d':' -f2");
    if (cpuInfo.isEmpty()) {
        cpuInfo = executeCommand("cat /proc/cpuinfo | grep 'Hardware' | head -1 | cut -d':' -f2");
    }
    if (cpuInfo.isEmpty()) {
        cpuInfo = executeCommand("cat /proc/cpuinfo | grep 'processor' | wc -l");
    }
    
    return cleanString(cpuInfo);
}

QString HardwareFingerprint::getMotherboardInfo()
{
    QString mbInfo;
    
    // Linux: 使用dmidecode获取主板信息
    mbInfo = executeCommand("sudo dmidecode -s baseboard-serial-number 2>/dev/null");
    if (mbInfo.isEmpty() || mbInfo.contains("Not Specified")) {
        mbInfo = executeCommand("sudo dmidecode -s baseboard-product-name 2>/dev/null");
    }
    if (mbInfo.isEmpty()) {
        // 备用方案：使用/sys/class/dmi/id/
        mbInfo = executeCommand("cat /sys/class/dmi/id/board_serial 2>/dev/null");
    }
    if (mbInfo.isEmpty()) {
        // ARM64设备特殊处理：使用设备树信息
        mbInfo = executeCommand("cat /proc/device-tree/model 2>/dev/null");
    }
    
    return cleanString(mbInfo);
}

QString HardwareFingerprint::getMacAddress()
{
    // 获取第一个有效的网卡MAC地址
    QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();
    
    for (const QNetworkInterface &interface : interfaces) {
        // 跳过回环接口和虚拟接口
        if (interface.flags() & QNetworkInterface::IsLoopBack ||
            interface.name().contains("docker") ||
            interface.name().contains("veth") ||
            interface.name().contains("br-") ||
            interface.name().contains("lo")) {
            continue;
        }
        
        QString mac = interface.hardwareAddress();
        if (!mac.isEmpty() && mac != "00:00:00:00:00:00") {
            return cleanString(mac);
        }
    }
    
    return QString();
}

QString HardwareFingerprint::getDiskSerial()
{
    QString diskSerial;
    
    // Linux: 获取根分区所在磁盘的序列号
    diskSerial = executeCommand("lsblk -no SERIAL $(df / | tail -1 | awk '{print $1}' | sed 's/[0-9]*$//')");
    if (diskSerial.isEmpty()) {
        // 备用方案：使用udevadm
        diskSerial = executeCommand("udevadm info --query=property --name=$(df / | tail -1 | awk '{print $1}' | sed 's/[0-9]*$//') | grep ID_SERIAL_SHORT | cut -d'=' -f2");
    }
    if (diskSerial.isEmpty()) {
        // 再备用方案：使用blkid
        diskSerial = executeCommand("blkid $(df / | tail -1 | awk '{print $1}' | sed 's/[0-9]*$//') | grep -o 'UUID=\"[^\"]*\"' | cut -d'\"' -f2");
    }
    
    return cleanString(diskSerial);
}

QString HardwareFingerprint::getSystemInfo()
{
    QString sysInfo;
    
    // 获取系统基本信息
    sysInfo += QSysInfo::kernelType();
    sysInfo += QSysInfo::kernelVersion();
    sysInfo += QSysInfo::currentCpuArchitecture();
    
    // 添加RK3588特定信息
    QString rk3588Info = executeCommand("cat /proc/device-tree/compatible 2>/dev/null");
    if (!rk3588Info.isEmpty()) {
        sysInfo += rk3588Info;
    }
    
    return cleanString(sysInfo);
}

bool HardwareFingerprint::verifyFingerprint(const QString &storedFingerprint, 
                                           const QString &currentFingerprint)
{
    QString current = currentFingerprint.isEmpty() ? getDeviceFingerprint() : currentFingerprint;
    
    qDebug() << "[硬件指纹] 验证指纹:";
    qDebug() << "  存储的:" << storedFingerprint;
    qDebug() << "  当前的:" << current;
    
    return storedFingerprint == current;
}

QString HardwareFingerprint::executeCommand(const QString &command)
{
    QProcess process;
    process.start("bash", QStringList() << "-c" << command);
    process.waitForFinished(5000); // 5秒超时
    
    if (process.exitCode() == 0) {
        return QString::fromLocal8Bit(process.readAllStandardOutput()).trimmed();
    }
    
    return QString();
}

QString HardwareFingerprint::cleanString(const QString &input)
{
    QString cleaned = input;
    
    // 移除空白字符
    cleaned = cleaned.trimmed();
    
    // 移除常见的无效值
    QStringList invalidValues = {
        "Not Specified", "Not Available", "To Be Filled By O.E.M.",
        "Default string", "System Serial Number", "System Product Name",
        "N/A", "Unknown", "None", ""
    };
    
    for (const QString &invalid : invalidValues) {
        if (cleaned.contains(invalid, Qt::CaseInsensitive)) {
            return QString();
        }
    }
    
    // 移除特殊字符，只保留字母数字和连字符
    cleaned = cleaned.replace(QRegularExpression("[^a-zA-Z0-9\\-]"), "");
    
    return cleaned;
}

QString HardwareFingerprint::generateMD5(const QString &input)
{
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(input.toUtf8());
    return hash.result().toHex();
}
