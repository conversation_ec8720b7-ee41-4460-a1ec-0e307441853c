#!/bin/bash
# RK3588边缘设备部署脚本
# 用于在RK3588设备上部署和运行YOLO模型

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }
echo_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }
echo_cmd() { echo -e "${CYAN}[CMD]${NC} $1"; }

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="${SCRIPT_DIR}/yolo_deploy"
LOG_DIR="${SCRIPT_DIR}/logs"

# 默认配置
DEFAULT_MODEL_TYPE="yolov8"
DEFAULT_INPUT_SOURCE="camera"
DEFAULT_CAMERA_ID="0"

# 参数变量
DEPLOY_PACKAGE=""
MODEL_TYPE=""
INPUT_SOURCE=""
OUTPUT_PATH=""
CAMERA_ID=""
VERBOSE=false

# 显示帮助信息
show_help() {
    cat << EOF
RK3588边缘设备YOLO模型部署脚本

用法: $0 [选项]

部署模式:
  --deploy PACKAGE_PATH  部署模型包 (.tar.gz格式)

运行模式:
  --run                  运行已部署的模型
  --model MODEL_TYPE     模型类型 (yolov5 或 yolov8，默认: yolov8)
  --input INPUT_SOURCE   输入源 (camera/image/video，默认: camera)
  --camera CAMERA_ID     摄像头ID (默认: 0)
  --output OUTPUT_PATH   输出路径 (可选)

通用选项:
  --verbose              详细输出
  --help                 显示此帮助信息

示例:
  # 部署模型包
  $0 --deploy yolov8_rk3588_deploy_20241201_143022.tar.gz

  # 运行摄像头推理
  $0 --run --model yolov8 --input camera --camera 0

  # 处理图像文件
  $0 --run --model yolov8 --input image --output result.jpg input.jpg

  # 处理视频文件
  $0 --run --model yolov8 --input video --output result.mp4 input.mp4

支持的输入源:
  - camera: 摄像头实时推理
  - image: 单张图像处理
  - video: 视频文件处理

EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --deploy)
                DEPLOY_PACKAGE="$2"
                shift 2
                ;;
            --run)
                RUN_MODE=true
                shift
                ;;
            --model)
                MODEL_TYPE="$2"
                shift 2
                ;;
            --input)
                INPUT_SOURCE="$2"
                shift 2
                ;;
            --camera)
                CAMERA_ID="$2"
                shift 2
                ;;
            --output)
                OUTPUT_PATH="$2"
                shift 2
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                # 处理位置参数（输入文件）
                if [[ -z "${INPUT_FILE}" ]]; then
                    INPUT_FILE="$1"
                fi
                shift
                ;;
        esac
    done
}

# 检查设备环境
check_device_environment() {
    echo_step "检查RK3588设备环境..."
    
    # 检查CPU架构
    ARCH=$(uname -m)
    if [[ "${ARCH}" != "aarch64" ]]; then
        echo_warning "⚠️ 当前架构不是aarch64: ${ARCH}"
    else
        echo_success "✓ CPU架构: ${ARCH}"
    fi
    
    # 检查内存
    MEMORY_MB=$(free -m | awk '/^Mem:/{print $2}')
    echo_info "系统内存: ${MEMORY_MB}MB"
    if [[ ${MEMORY_MB} -lt 4096 ]]; then
        echo_warning "⚠️ 内存较少，可能影响性能: ${MEMORY_MB}MB"
    fi
    
    # 检查NPU设备
    if [[ -e "/dev/rknpu" ]]; then
        echo_success "✓ RKNN NPU设备可用"
    else
        echo_warning "⚠️ 未检测到RKNN NPU设备"
    fi
    
    # 检查摄像头设备
    if [[ "${INPUT_SOURCE}" == "camera" ]]; then
        CAMERA_DEVICE="/dev/video${CAMERA_ID:-0}"
        if [[ -e "${CAMERA_DEVICE}" ]]; then
            echo_success "✓ 摄像头设备可用: ${CAMERA_DEVICE}"
        else
            echo_warning "⚠️ 摄像头设备不可用: ${CAMERA_DEVICE}"
        fi
    fi
    
    echo_success "设备环境检查完成"
}

# 部署模型包
deploy_model_package() {
    echo_step "部署模型包..."
    
    # 检查部署包文件
    if [[ ! -f "${DEPLOY_PACKAGE}" ]]; then
        echo_error "部署包文件不存在: ${DEPLOY_PACKAGE}"
        exit 1
    fi
    
    # 检查文件格式
    if [[ "${DEPLOY_PACKAGE}" != *.tar.gz ]]; then
        echo_error "部署包必须是.tar.gz格式"
        exit 1
    fi
    
    # 创建部署目录
    mkdir -p "${DEPLOY_DIR}"
    mkdir -p "${LOG_DIR}"
    
    # 备份现有部署（如果存在）
    if [[ -d "${DEPLOY_DIR}" && "$(ls -A ${DEPLOY_DIR})" ]]; then
        BACKUP_DIR="${DEPLOY_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
        echo_info "备份现有部署到: ${BACKUP_DIR}"
        mv "${DEPLOY_DIR}" "${BACKUP_DIR}"
        mkdir -p "${DEPLOY_DIR}"
    fi
    
    # 解压部署包
    echo_info "解压部署包到: ${DEPLOY_DIR}"
    tar -xzf "${DEPLOY_PACKAGE}" -C "${DEPLOY_DIR}"
    
    # 验证部署内容
    echo_info "验证部署内容..."
    
    # 检查可执行文件
    EXECUTABLES=$(find "${DEPLOY_DIR}" -name "rknn_*_demo" -type f)
    if [[ -z "${EXECUTABLES}" ]]; then
        echo_error "未找到可执行文件"
        exit 1
    fi
    
    for exe in ${EXECUTABLES}; do
        chmod +x "${exe}"
        echo_success "✓ 可执行文件: $(basename ${exe})"
    done
    
    # 检查模型文件
    MODEL_FILES=$(find "${DEPLOY_DIR}" -name "*.rknn" -type f)
    if [[ -z "${MODEL_FILES}" ]]; then
        echo_error "未找到RKNN模型文件"
        exit 1
    fi
    
    for model in ${MODEL_FILES}; do
        MODEL_SIZE=$(du -h "${model}" | cut -f1)
        echo_success "✓ 模型文件: $(basename ${model}) (${MODEL_SIZE})"
    done
    
    # 检查库文件
    LIB_DIR="${DEPLOY_DIR}/lib"
    if [[ -d "${LIB_DIR}" ]]; then
        LIB_COUNT=$(find "${LIB_DIR}" -name "*.so*" | wc -l)
        echo_success "✓ 依赖库: ${LIB_COUNT}个文件"
    fi
    
    echo_success "✓ 模型包部署完成"
}

# 设置运行环境
setup_runtime_environment() {
    echo_step "设置运行环境..."
    
    # 设置库路径
    LIB_DIR="${DEPLOY_DIR}/lib"
    if [[ -d "${LIB_DIR}" ]]; then
        export LD_LIBRARY_PATH="${LIB_DIR}:${LD_LIBRARY_PATH}"
        echo_success "✓ 库路径已设置: ${LIB_DIR}"
    fi
    
    # 检查依赖
    EXECUTABLE="${DEPLOY_DIR}/rknn_${MODEL_TYPE}_demo"
    if [[ -f "${EXECUTABLE}" ]]; then
        echo_info "检查可执行文件依赖..."
        if command -v ldd &> /dev/null; then
            MISSING_LIBS=$(ldd "${EXECUTABLE}" 2>/dev/null | grep "not found" || true)
            if [[ -n "${MISSING_LIBS}" ]]; then
                echo_warning "⚠️ 发现缺失的依赖库:"
                echo "${MISSING_LIBS}"
            else
                echo_success "✓ 所有依赖库都可用"
            fi
        fi
    fi
    
    echo_success "运行环境设置完成"
}

# 运行模型推理
run_model_inference() {
    echo_step "运行模型推理..."
    
    # 设置默认值
    MODEL_TYPE="${MODEL_TYPE:-${DEFAULT_MODEL_TYPE}}"
    INPUT_SOURCE="${INPUT_SOURCE:-${DEFAULT_INPUT_SOURCE}}"
    CAMERA_ID="${CAMERA_ID:-${DEFAULT_CAMERA_ID}}"
    
    # 检查可执行文件
    EXECUTABLE="${DEPLOY_DIR}/rknn_${MODEL_TYPE}_demo"
    if [[ ! -f "${EXECUTABLE}" ]]; then
        echo_error "未找到可执行文件: ${EXECUTABLE}"
        echo_info "可用的模型类型:"
        find "${DEPLOY_DIR}" -name "rknn_*_demo" -type f | sed 's/.*rknn_\(.*\)_demo/  \1/'
        exit 1
    fi
    
    # 检查模型文件
    MODEL_FILE="${DEPLOY_DIR}/model/${MODEL_TYPE}.rknn"
    if [[ ! -f "${MODEL_FILE}" ]]; then
        # 尝试查找模型文件
        MODEL_FILE=$(find "${DEPLOY_DIR}" -name "*${MODEL_TYPE}*.rknn" -type f | head -1)
        if [[ -z "${MODEL_FILE}" ]]; then
            echo_error "未找到模型文件: ${MODEL_TYPE}.rknn"
            exit 1
        fi
    fi
    
    echo_success "✓ 使用模型: ${MODEL_FILE}"
    
    # 构建运行命令
    RUN_CMD="${EXECUTABLE} ${MODEL_FILE}"
    
    case "${INPUT_SOURCE}" in
        camera)
            RUN_CMD="${RUN_CMD} ${CAMERA_ID}"
            if [[ -n "${OUTPUT_PATH}" ]]; then
                RUN_CMD="${RUN_CMD} ${OUTPUT_PATH}"
            fi
            echo_info "摄像头推理模式，摄像头ID: ${CAMERA_ID}"
            ;;
        image)
            if [[ -z "${INPUT_FILE}" ]]; then
                echo_error "图像模式需要指定输入文件"
                exit 1
            fi
            RUN_CMD="${RUN_CMD} ${INPUT_FILE}"
            if [[ -n "${OUTPUT_PATH}" ]]; then
                RUN_CMD="${RUN_CMD} ${OUTPUT_PATH}"
            fi
            echo_info "图像处理模式，输入: ${INPUT_FILE}"
            ;;
        video)
            if [[ -z "${INPUT_FILE}" ]]; then
                echo_error "视频模式需要指定输入文件"
                exit 1
            fi
            RUN_CMD="${RUN_CMD} ${INPUT_FILE}"
            if [[ -n "${OUTPUT_PATH}" ]]; then
                RUN_CMD="${RUN_CMD} ${OUTPUT_PATH}"
            fi
            echo_info "视频处理模式，输入: ${INPUT_FILE}"
            ;;
        *)
            echo_error "不支持的输入源: ${INPUT_SOURCE}"
            echo_info "支持的输入源: camera, image, video"
            exit 1
            ;;
    esac
    
    # 记录运行日志
    LOG_FILE="${LOG_DIR}/inference_$(date +%Y%m%d_%H%M%S).log"
    
    echo_info "执行推理命令..."
    if [[ "${VERBOSE}" == "true" ]]; then
        echo_cmd "${RUN_CMD}"
    fi
    
    # 记录开始时间
    START_TIME=$(date +%s)
    
    # 执行推理
    cd "${DEPLOY_DIR}"
    if [[ "${VERBOSE}" == "true" ]]; then
        eval "${RUN_CMD}" 2>&1 | tee "${LOG_FILE}"
    else
        eval "${RUN_CMD}" > "${LOG_FILE}" 2>&1
    fi
    
    # 记录结束时间
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    echo_success "✓ 推理完成，耗时: ${DURATION}秒"
    echo_info "运行日志: ${LOG_FILE}"
    
    if [[ -n "${OUTPUT_PATH}" && -f "${OUTPUT_PATH}" ]]; then
        OUTPUT_SIZE=$(du -h "${OUTPUT_PATH}" | cut -f1)
        echo_success "✓ 输出文件: ${OUTPUT_PATH} (${OUTPUT_SIZE})"
    fi
}

# 主函数
main() {
    echo_step "=== RK3588边缘设备YOLO模型部署脚本 ==="
    
    # 解析参数
    parse_arguments "$@"
    
    # 检查设备环境
    check_device_environment
    
    # 执行相应操作
    if [[ -n "${DEPLOY_PACKAGE}" ]]; then
        # 部署模式
        deploy_model_package
        echo_success "🎉 模型部署完成！"
        echo_info "使用以下命令运行推理:"
        echo_cmd "$0 --run --model ${DEFAULT_MODEL_TYPE} --input camera"
    elif [[ "${RUN_MODE}" == "true" ]]; then
        # 运行模式
        setup_runtime_environment
        run_model_inference
        echo_success "🎉 模型推理完成！"
    else
        echo_error "请指定操作模式: --deploy 或 --run"
        show_help
        exit 1
    fi
    
    echo_success "脚本执行完成！"
}

# 执行主函数
main "$@"
