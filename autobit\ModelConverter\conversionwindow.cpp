#include "conversionwindow.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QFileDialog>
#include <QDesktopServices>
#include <QDir>
#include <QCoreApplication>

ConversionWindow::ConversionWindow(QWidget *parent)
    : QWidget(parent), process(new QProcess(this))
{
    setupUI();
    setupConnections();
}

void ConversionWindow::setupUI()
{
    modelTypeCombo = new QComboBox(this);
    modelTypeCombo->addItems({"yolov5", "yolov8"});

    modelPathEdit = new QLineEdit(this);
    browseButton = new QPushButton("浏览", this);
    convertButton = new QPushButton("转换", this);
    resultDisplay = new QTextEdit(this);
    resultDisplay->setReadOnly(true);

    statusLabel = new QLabel("等待转换", this);
    spinnerLabel = new QLabel(this);
    spinnerLabel->setFixedWidth(20);
    spinnerLabel->setAlignment(Qt::AlignCenter);

    QHBoxLayout *topLayout = new QHBoxLayout;
    topLayout->addWidget(modelTypeCombo);
    topLayout->addWidget(modelPathEdit);
    topLayout->addWidget(browseButton);
    topLayout->addWidget(convertButton);
    topLayout->addWidget(spinnerLabel);
    topLayout->addWidget(statusLabel);

    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addLayout(topLayout);
    mainLayout->addWidget(resultDisplay);

    setLayout(mainLayout);
}

void ConversionWindow::setupConnections()
{
    connect(browseButton, &QPushButton::clicked, this, &ConversionWindow::onBrowseClicked);
    connect(convertButton, &QPushButton::clicked, this, &ConversionWindow::onConvertClicked);
    connect(process, &QProcess::readyReadStandardOutput, this, &ConversionWindow::onProcessOutput);
    connect(process, QOverload<int>::of(&QProcess::finished), this, &ConversionWindow::onProcessFinished);
    connect(process, &QProcess::readyReadStandardError, this, &ConversionWindow::onProcessErrorOutput);

}

void ConversionWindow::onBrowseClicked()
{
    QString filePath = QFileDialog::getOpenFileName(this, QString::fromUtf8("选择模型文件"), "", "Model Files (*.pt *.onnx)");
    if (!filePath.isEmpty()) {
        modelPathEdit->setText(filePath);
    }
}

void ConversionWindow::onConvertClicked() {
    convertButton->setEnabled(false);
    convertButton->setText("转换中...");

    QString modelPath = modelPathEdit->text().trimmed();
    if (modelPath.isEmpty()) {
        resultDisplay->append("❌ 请先选择模型文件");
        convertButton->setEnabled(true);
        convertButton->setText("转换");
        return;
    }

    QString modelType = modelTypeCombo->currentText().trimmed();
    QString fileName = QFileInfo(modelPath).fileName();

    // 确保 build 目录结构存在
    QString buildDir = QCoreApplication::applicationDirPath();
    QString modelsDir = buildDir + "/models";
    QString resultDir = buildDir + "/result";

    // 创建模型目录（如果不存在）
    QDir().mkpath(modelsDir);
    QDir().mkpath(resultDir);

    // 复制模型文件到 build 目录
    QString destinationModelPath = modelsDir + "/" + fileName;
    if (QFile::exists(destinationModelPath)) {
        QFile::remove(destinationModelPath);
    }
    if (!QFile::copy(modelPath, destinationModelPath)) {
        resultDisplay->append("❌ 无法复制模型文件到: " + destinationModelPath);
        convertButton->setEnabled(true);
        convertButton->setText("转换");
        return;
    }

    QString dockerModelPath = "/workspace/yolo-rk3588/models";
    QString dockerOutputPath = "result";

    // 构造 Docker 命令
    QString dockerCommand = QString(
        "docker run --rm "
        "-v \"%1\":%2 "
        "-v \"%3\":/workspace/yolo-rk3588/result "
        "-w /workspace/yolo-rk3588 "
        "model-converter:latest "
        "--model %4 --weight %5 --output %6")
        .arg(modelsDir)
        .arg(dockerModelPath)
        .arg(resultDir)
        .arg(modelType)
        .arg(dockerModelPath + "/" + fileName)  // 容器内绝对路径
        .arg(dockerOutputPath);

    // 启动转换进程
    process = new QProcess(this);
    connect(process, &QProcess::readyReadStandardOutput, this, [=]() {
        resultDisplay->append(QString::fromLocal8Bit(process->readAllStandardOutput()));
    });
    connect(process, &QProcess::readyReadStandardError, this, [=]() {
        resultDisplay->append(QString::fromLocal8Bit(process->readAllStandardError()));
    });
    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished), this, [=](int, QProcess::ExitStatus) {
        convertButton->setEnabled(true);
        convertButton->setText("转换");
        process->deleteLater();
    });

    resultDisplay->append("✅ 模型文件已复制到: " + destinationModelPath);
    resultDisplay->append("🚀 正在执行 Docker 转换命令...");
    resultDisplay->append(dockerCommand);

    process->start("bash", QStringList() << "-c" << dockerCommand);
}


void ConversionWindow::onProcessFinished(int exitCode)
{
    QString buildDir = QCoreApplication::applicationDirPath();
    QString resultPath = buildDir + "/result";

    if (exitCode == 0) {
        statusLabel->setText(QString::fromUtf8("✅ 转换成功"));
        resultDisplay->append(QString::fromUtf8("转换结果已保存到: ") + resultPath);
        QDesktopServices::openUrl(QUrl::fromLocalFile(resultPath));
    } else {
        statusLabel->setText(QString::fromUtf8("❌ 转换失败"));
    }
}



void ConversionWindow::onProcessOutput()
{
    QByteArray output = process->readAllStandardOutput();
    resultDisplay->append(QString::fromLocal8Bit(output));
}

void ConversionWindow::onProcessErrorOutput()
{
    QByteArray errorOutput = process->readAllStandardError();
    resultDisplay->append(QString::fromLocal8Bit(errorOutput));
}






