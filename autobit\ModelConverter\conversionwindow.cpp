#include "conversionwindow.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QFileDialog>
#include <QDesktopServices>
#include <QDir>
#include <QCoreApplication>
#include <QApplication>
#include <QScreen>
#include <QMessageBox>
#include <QDateTime>
#include <QFileInfo>
#include <QDebug>

ConversionWindow::ConversionWindow(QWidget *parent)
    : QWidget(parent), process(nullptr), isConverting(false)
{
    setupUI();
    setupStyles();
    setupConnections();
}

void ConversionWindow::setupUI()
{
    setWindowTitle("AutoBit 模型转换工具");
    setMinimumSize(900, 700);
    resize(1200, 800);

    // 居中显示
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);

    // 主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    mainLayout->setSpacing(15);

    // 标题区域
    headerFrame = new QFrame(this);
    headerFrame->setObjectName("headerFrame");

    titleLabel = new QLabel("🚀 YOLO 模型转换工具", this);
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);

    statusIconLabel = new QLabel("⚡", this);
    statusIconLabel->setObjectName("statusIconLabel");
    statusIconLabel->setAlignment(Qt::AlignCenter);

    QHBoxLayout *headerLayout = new QHBoxLayout(headerFrame);
    headerLayout->addWidget(statusIconLabel);
    headerLayout->addWidget(titleLabel, 1);
    headerLayout->addStretch();

    // 输入区域
    inputFrame = new QFrame(this);
    inputFrame->setObjectName("inputFrame");

    // 模型选择组
    modelGroup = new QGroupBox("模型配置", this);
    modelGroup->setObjectName("modelGroup");

    QLabel *typeLabel = new QLabel("模型类型:", this);
    modelTypeCombo = new QComboBox(this);
    modelTypeCombo->setObjectName("modelTypeCombo");
    modelTypeCombo->addItems({"yolov5", "yolov8"});
    modelTypeCombo->setCurrentText("yolov5");

    QLabel *pathLabel = new QLabel("模型文件:", this);
    modelPathEdit = new QLineEdit(this);
    modelPathEdit->setObjectName("modelPathEdit");
    modelPathEdit->setPlaceholderText("请选择 .pt 或 .onnx 模型文件");

    browseButton = new QPushButton("📁 浏览", this);
    browseButton->setObjectName("browseButton");

    QGridLayout *modelLayout = new QGridLayout(modelGroup);
    modelLayout->addWidget(typeLabel, 0, 0);
    modelLayout->addWidget(modelTypeCombo, 0, 1);
    modelLayout->addWidget(pathLabel, 1, 0);
    modelLayout->addWidget(modelPathEdit, 1, 1);
    modelLayout->addWidget(browseButton, 1, 2);
    modelLayout->setColumnStretch(1, 1);

    // 操作区域
    actionGroup = new QGroupBox("操作", this);
    actionGroup->setObjectName("actionGroup");

    convertButton = new QPushButton("🔄 开始转换", this);
    convertButton->setObjectName("convertButton");

    clearLogButton = new QPushButton("🗑️ 清空日志", this);
    clearLogButton->setObjectName("clearLogButton");

    openResultButton = new QPushButton("📂 打开结果目录", this);
    openResultButton->setObjectName("openResultButton");

    progressBar = new QProgressBar(this);
    progressBar->setObjectName("progressBar");
    progressBar->setVisible(false);
    progressBar->setRange(0, 100);

    progressLabel = new QLabel("准备就绪", this);
    progressLabel->setObjectName("progressLabel");
    progressLabel->setAlignment(Qt::AlignCenter);

    statusLabel = new QLabel("请选择模型文件开始转换", this);
    statusLabel->setObjectName("statusLabel");
    statusLabel->setAlignment(Qt::AlignCenter);

    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(convertButton);
    buttonLayout->addWidget(clearLogButton);
    buttonLayout->addWidget(openResultButton);
    buttonLayout->addStretch();

    QVBoxLayout *actionLayout = new QVBoxLayout(actionGroup);
    actionLayout->addLayout(buttonLayout);
    actionLayout->addWidget(progressBar);
    actionLayout->addWidget(progressLabel);
    actionLayout->addWidget(statusLabel);

    QHBoxLayout *inputLayout = new QHBoxLayout(inputFrame);
    inputLayout->addWidget(modelGroup, 2);
    inputLayout->addWidget(actionGroup, 1);

    // 输出区域
    outputFrame = new QFrame(this);
    outputFrame->setObjectName("outputFrame");

    outputTabs = new QTabWidget(this);
    outputTabs->setObjectName("outputTabs");

    // 转换日志标签页
    logDisplay = new QTextEdit(this);
    logDisplay->setObjectName("logDisplay");
    logDisplay->setReadOnly(true);
    logDisplay->setFont(QFont("Consolas", 10));
    outputTabs->addTab(logDisplay, "📋 转换日志");

    // 错误日志标签页
    errorDisplay = new QTextEdit(this);
    errorDisplay->setObjectName("errorDisplay");
    errorDisplay->setReadOnly(true);
    errorDisplay->setFont(QFont("Consolas", 10));
    outputTabs->addTab(errorDisplay, "❌ 错误日志");

    // 结果列表标签页
    resultList = new QListWidget(this);
    resultList->setObjectName("resultList");
    outputTabs->addTab(resultList, "📁 转换结果");

    QVBoxLayout *outputLayout = new QVBoxLayout(outputFrame);
    outputLayout->addWidget(outputTabs);

    // 主布局组装
    mainLayout->addWidget(headerFrame);
    mainLayout->addWidget(inputFrame);
    mainLayout->addWidget(outputFrame, 1);

    // 初始化日志
    addLogMessage("AutoBit 模型转换工具已启动", "info");
    addLogMessage("支持的模型格式: .pt, .onnx", "info");
    addLogMessage("支持的模型类型: YOLOv5, YOLOv8", "info");
}

void ConversionWindow::setupStyles()
{
    setStyleSheet(R"(
        QWidget {
            background-color: #f8f9fa;
            font-family: "Microsoft YaHei", "SimHei", sans-serif;
        }

        #headerFrame {
            background-color: #2c3e50;
            border-radius: 8px;
            padding: 10px;
        }

        #titleLabel {
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        #statusIconLabel {
            color: #f39c12;
            font-size: 24px;
        }

        #inputFrame, #outputFrame {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }

        QGroupBox {
            font-size: 14px;
            font-weight: bold;
            color: #495057;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            background-color: white;
        }

        #modelTypeCombo, #modelPathEdit {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 13px;
            background-color: white;
        }

        #modelTypeCombo:focus, #modelPathEdit:focus {
            border-color: #007bff;
            outline: none;
        }

        #browseButton {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 13px;
        }

        #browseButton:hover {
            background-color: #5a6268;
        }

        #convertButton {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            min-width: 120px;
        }

        #convertButton:hover {
            background-color: #0056b3;
        }

        #convertButton:disabled {
            background-color: #6c757d;
        }

        #clearLogButton, #openResultButton {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 13px;
        }

        #clearLogButton:hover, #openResultButton:hover {
            background-color: #218838;
        }

        #progressBar {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: #e9ecef;
            text-align: center;
        }

        #progressBar::chunk {
            background-color: #007bff;
            border-radius: 3px;
        }

        #progressLabel, #statusLabel {
            font-size: 14px;
            color: #495057;
            padding: 5px;
        }

        #statusLabel[type="success"] {
            color: #28a745;
            font-weight: bold;
        }

        #statusLabel[type="error"] {
            color: #dc3545;
            font-weight: bold;
        }

        #statusLabel[type="warning"] {
            color: #ffc107;
            font-weight: bold;
        }

        QTabWidget::pane {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: white;
        }

        QTabBar::tab {
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            padding: 8px 16px;
            margin-right: 2px;
        }

        QTabBar::tab:selected {
            background-color: white;
            border-bottom-color: white;
        }

        #logDisplay, #errorDisplay {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 11px;
        }

        #resultList {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            alternate-background-color: #f8f9fa;
        }
    )");
}

void ConversionWindow::setupConnections()
{
    connect(browseButton, &QPushButton::clicked, this, &ConversionWindow::onBrowseClicked);
    connect(convertButton, &QPushButton::clicked, this, &ConversionWindow::onConvertClicked);
    connect(clearLogButton, &QPushButton::clicked, this, &ConversionWindow::onClearLogClicked);
    connect(openResultButton, &QPushButton::clicked, this, &ConversionWindow::onOpenResultClicked);
}

void ConversionWindow::onBrowseClicked()
{
    QString filePath = QFileDialog::getOpenFileName(
        this,
        "选择模型文件",
        "",
        "模型文件 (*.pt *.onnx);;PyTorch模型 (*.pt);;ONNX模型 (*.onnx);;所有文件 (*)"
    );

    if (!filePath.isEmpty()) {
        modelPathEdit->setText(filePath);
        addLogMessage(QString("已选择模型文件: %1").arg(QFileInfo(filePath).fileName()), "info");

        // 根据文件扩展名自动设置模型类型
        QString extension = QFileInfo(filePath).suffix().toLower();
        if (extension == "pt") {
            // PyTorch模型，默认设置为yolov5
            if (modelTypeCombo->findText("yolov5") != -1) {
                modelTypeCombo->setCurrentText("yolov5");
            }
        }

        setConversionStatus("已选择模型文件，可以开始转换", "info");
    }
}

void ConversionWindow::onConvertClicked()
{
    if (isConverting) {
        return;
    }

    QString modelPath = modelPathEdit->text().trimmed();
    if (modelPath.isEmpty()) {
        setConversionStatus("请先选择模型文件", "error");
        QMessageBox::warning(this, "错误", "请先选择模型文件");
        return;
    }

    if (!QFile::exists(modelPath)) {
        setConversionStatus("模型文件不存在", "error");
        QMessageBox::warning(this, "错误", "选择的模型文件不存在");
        return;
    }

    QString modelType = modelTypeCombo->currentText().trimmed();
    QString fileName = QFileInfo(modelPath).fileName();

    addLogMessage("=== 开始模型转换 ===", "info");
    addLogMessage(QString("模型类型: %1").arg(modelType), "info");
    addLogMessage(QString("模型文件: %1").arg(fileName), "info");
    addLogMessage(QString("文件大小: %1 MB").arg(QFileInfo(modelPath).size() / 1024.0 / 1024.0, 0, 'f', 2), "info");

    // 设置转换状态
    isConverting = true;
    convertButton->setEnabled(false);
    convertButton->setText("🔄 转换中...");
    progressBar->setVisible(true);
    progressBar->setRange(0, 0); // 无限进度条
    setConversionStatus("正在准备转换环境...", "info");

    // 确保build目录结构存在
    QString buildDir = QCoreApplication::applicationDirPath();
    QString modelsDir = buildDir + "/models";
    QString resultDir = buildDir + "/result";

    QDir().mkpath(modelsDir);
    QDir().mkpath(resultDir);

    // 复制模型文件到build目录
    QString destinationModelPath = modelsDir + "/" + fileName;
    if (QFile::exists(destinationModelPath)) {
        QFile::remove(destinationModelPath);
    }

    if (!QFile::copy(modelPath, destinationModelPath)) {
        addLogMessage("❌ 无法复制模型文件到: " + destinationModelPath, "error");
        resetUI();
        return;
    }

    addLogMessage("✅ 模型文件已复制到工作目录", "info");
    setConversionStatus("正在启动Docker转换...", "info");

    // 构造Docker命令
    QString dockerModelPath = "/workspace/yolo-rk3588/models";
    QString dockerOutputPath = "result";

    QString dockerCommand = QString(
        "docker run --rm "
        "-v \"%1\":%2 "
        "-v \"%3\":/workspace/yolo-rk3588/result "
        "-w /workspace/yolo-rk3588 "
        "model-converter:latest "
        "--model %4 --weight %5 --output %6")
        .arg(modelsDir)
        .arg(dockerModelPath)
        .arg(resultDir)
        .arg(modelType)
        .arg(dockerModelPath + "/" + fileName)
        .arg(dockerOutputPath);

    addLogMessage("🚀 Docker转换命令:", "info");
    addLogMessage(dockerCommand, "info");

    // 启动转换进程
    process = new QProcess(this);
    connect(process, &QProcess::readyReadStandardOutput, this, &ConversionWindow::onProcessOutput);
    connect(process, &QProcess::readyReadStandardError, this, &ConversionWindow::onProcessErrorOutput);
    connect(process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &ConversionWindow::onProcessFinished);

    process->start("bash", QStringList() << "-c" << dockerCommand);

    if (!process->waitForStarted(5000)) {
        addLogMessage("❌ 无法启动Docker进程", "error");
        setConversionStatus("Docker启动失败", "error");
        resetUI();
        return;
    }

    addLogMessage("✅ Docker转换进程已启动", "info");
    setConversionStatus("正在转换模型，请稍候...", "info");
}

void ConversionWindow::onProcessOutput()
{
    if (!process) return;

    QByteArray output = process->readAllStandardOutput();
    QString text = QString::fromLocal8Bit(output);

    // 分行处理输出
    QStringList lines = text.split('\n', QString::SkipEmptyParts);
    for (const QString &line : lines) {
        if (!line.trimmed().isEmpty()) {
            addLogMessage(line.trimmed(), "info");
        }
    }
}

void ConversionWindow::onProcessErrorOutput()
{
    if (!process) return;

    QByteArray errorOutput = process->readAllStandardError();
    QString text = QString::fromLocal8Bit(errorOutput);

    // 分行处理错误输出
    QStringList lines = text.split('\n', QString::SkipEmptyParts);
    for (const QString &line : lines) {
        if (!line.trimmed().isEmpty()) {
            addLogMessage(line.trimmed(), "error");
            errorDisplay->append(QString("[%1] %2")
                .arg(QDateTime::currentDateTime().toString("hh:mm:ss"))
                .arg(line.trimmed()));
        }
    }
}

void ConversionWindow::onProcessFinished(int exitCode)
{
    if (!process) return;

    QString buildDir = QCoreApplication::applicationDirPath();
    QString resultPath = buildDir + "/result";

    addLogMessage("=== 转换完成 ===", "info");
    addLogMessage(QString("退出代码: %1").arg(exitCode), "info");

    if (exitCode == 0) {
        setConversionStatus("✅ 转换成功完成", "success");
        addLogMessage("✅ 模型转换成功", "success");
        addLogMessage(QString("结果保存路径: %1").arg(resultPath), "success");

        // 更新结果列表
        updateResultList();

        // 切换到结果标签页
        outputTabs->setCurrentIndex(2);

        QMessageBox::information(this, "转换成功",
            QString("模型转换成功完成！\n\n结果已保存到:\n%1").arg(resultPath));
    } else {
        setConversionStatus("❌ 转换失败", "error");
        addLogMessage("❌ 模型转换失败", "error");

        // 切换到错误日志标签页
        outputTabs->setCurrentIndex(1);

        QMessageBox::warning(this, "转换失败",
            QString("模型转换失败，退出代码: %1\n\n请查看错误日志获取详细信息。").arg(exitCode));
    }

    resetUI();
    process->deleteLater();
    process = nullptr;
}

void ConversionWindow::onClearLogClicked()
{
    logDisplay->clear();
    errorDisplay->clear();
    resultList->clear();

    addLogMessage("日志已清空", "info");
    setConversionStatus("日志已清空", "info");
}

void ConversionWindow::onOpenResultClicked()
{
    QString buildDir = QCoreApplication::applicationDirPath();
    QString resultPath = buildDir + "/result";

    if (QDir(resultPath).exists()) {
        QDesktopServices::openUrl(QUrl::fromLocalFile(resultPath));
        addLogMessage(QString("已打开结果目录: %1").arg(resultPath), "info");
    } else {
        QMessageBox::warning(this, "错误", "结果目录不存在");
        addLogMessage("结果目录不存在", "error");
    }
}

void ConversionWindow::updateProgress(int value)
{
    progressBar->setValue(value);
    progressLabel->setText(QString("转换进度: %1%").arg(value));
}

void ConversionWindow::setConversionStatus(const QString &status, const QString &type)
{
    statusLabel->setText(status);
    statusLabel->setProperty("type", type);
    statusLabel->style()->unpolish(statusLabel);
    statusLabel->style()->polish(statusLabel);

    // 更新状态图标
    if (type == "success") {
        statusIconLabel->setText("✅");
    } else if (type == "error") {
        statusIconLabel->setText("❌");
    } else if (type == "warning") {
        statusIconLabel->setText("⚠️");
    } else {
        statusIconLabel->setText("⚡");
    }

    qDebug() << "[转换窗口] 状态更新:" << type << "-" << status;
}

void ConversionWindow::addLogMessage(const QString &message, const QString &type)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString prefix;

    if (type == "success") {
        prefix = "✅";
    } else if (type == "error") {
        prefix = "❌";
    } else if (type == "warning") {
        prefix = "⚠️";
    } else {
        prefix = "ℹ️";
    }

    QString formattedMessage = QString("[%1] %2 %3").arg(timestamp).arg(prefix).arg(message);
    logDisplay->append(formattedMessage);

    // 自动滚动到底部
    QTextCursor cursor = logDisplay->textCursor();
    cursor.movePosition(QTextCursor::End);
    logDisplay->setTextCursor(cursor);
}

void ConversionWindow::resetUI()
{
    isConverting = false;
    convertButton->setEnabled(true);
    convertButton->setText("🔄 开始转换");
    progressBar->setVisible(false);
    progressLabel->setText("准备就绪");
}

void ConversionWindow::updateResultList()
{
    resultList->clear();

    QString buildDir = QCoreApplication::applicationDirPath();
    QString resultPath = buildDir + "/result";

    QDir resultDir(resultPath);
    if (!resultDir.exists()) {
        return;
    }

    QStringList filters;
    filters << "*.rknn" << "*.bin" << "*.json" << "*.txt" << "*.log";

    QFileInfoList files = resultDir.entryInfoList(filters, QDir::Files, QDir::Time);

    for (const QFileInfo &fileInfo : files) {
        QString itemText = QString("%1 (%2 KB)")
            .arg(fileInfo.fileName())
            .arg(fileInfo.size() / 1024);

        QListWidgetItem *item = new QListWidgetItem(itemText);
        item->setData(Qt::UserRole, fileInfo.absoluteFilePath());

        // 根据文件类型设置图标
        QString suffix = fileInfo.suffix().toLower();
        if (suffix == "rknn") {
            item->setText("🎯 " + itemText);
        } else if (suffix == "json") {
            item->setText("📄 " + itemText);
        } else if (suffix == "log") {
            item->setText("📋 " + itemText);
        } else {
            item->setText("📁 " + itemText);
        }

        resultList->addItem(item);
    }

    if (files.isEmpty()) {
        QListWidgetItem *item = new QListWidgetItem("暂无转换结果");
        item->setFlags(item->flags() & ~Qt::ItemIsSelectable);
        resultList->addItem(item);
    }
}








