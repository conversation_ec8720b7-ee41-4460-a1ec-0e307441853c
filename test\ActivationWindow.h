#ifndef ACTIVATIONWINDOW_H
#define ACTIVATIONWINDOW_H

#include <QWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFrame>
#include <QPropertyAnimation>
#include <QTimer>
#include <QCheckBox>
#include <QGroupBox>
#include <QProgressBar>
#include <QMessageBox>
#include "LicenseManager.h"

/**
 * @brief 激活窗口类
 * 
 * 提供许可证激活界面，支持双模式激活
 * 适用于RK3588边缘设备的医疗诊断应用
 */
class ActivationWindow : public QWidget
{
    Q_OBJECT

public:
    explicit ActivationWindow(QWidget *parent = nullptr);

signals:
    /**
     * @brief 激活成功信号
     */
    void activationSuccessful();

private slots:
    void onActivateClicked();
    void onDebugModeChanged(bool enabled);
    void showDeviceInfo();
    void onActivationFinished(LicenseManager::ActivationResult result, const QString &message);
    void onLicenseStatusChanged(bool isValid);

private:
    void setupUI();
    void setupStyles();
    void setupAnimations();
    void setStatus(const QString &message, const QString &type = "info");
    void showActivationSuccess();
    void showActivationError(const QString &error);

    // 激活逻辑
    QString getCurrentDeviceId();

    // UI组件
    QFrame *mainFrame;
    QLabel *titleLabel;
    QLabel *logoLabel;
    QLineEdit *licenseEdit;
    QPushButton *activateButton;
    QLabel *statusLabel;
    QLabel *deviceInfoLabel;
    QPushButton *deviceInfoButton;
    QCheckBox *debugModeCheckBox;
    QProgressBar *progressBar;
    QLabel *loadingLabel;

    // 动画
    QPropertyAnimation *statusAnimation;
    QPropertyAnimation *buttonAnimation;
    QTimer *statusTimer;

    // 许可证管理器
    LicenseManager *licenseManager;

    // 状态
    bool isDebugMode;
    bool isActivating;
};

#endif // ACTIVATIONWINDOW_H
