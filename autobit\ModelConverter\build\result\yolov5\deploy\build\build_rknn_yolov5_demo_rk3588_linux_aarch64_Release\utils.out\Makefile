# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspace/yolo-rk3588/yolov5/cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out/CMakeFiles/progress.marks
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f CMakeFiles/Makefile2 utils.out/all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f CMakeFiles/Makefile2 utils.out/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f CMakeFiles/Makefile2 utils.out/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f CMakeFiles/Makefile2 utils.out/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
utils.out/CMakeFiles/audioutils.dir/rule:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f CMakeFiles/Makefile2 utils.out/CMakeFiles/audioutils.dir/rule
.PHONY : utils.out/CMakeFiles/audioutils.dir/rule

# Convenience name for target.
audioutils: utils.out/CMakeFiles/audioutils.dir/rule

.PHONY : audioutils

# fast build rule for target.
audioutils/fast:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/audioutils.dir/build.make utils.out/CMakeFiles/audioutils.dir/build
.PHONY : audioutils/fast

# Convenience name for target.
utils.out/CMakeFiles/imageutils.dir/rule:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f CMakeFiles/Makefile2 utils.out/CMakeFiles/imageutils.dir/rule
.PHONY : utils.out/CMakeFiles/imageutils.dir/rule

# Convenience name for target.
imageutils: utils.out/CMakeFiles/imageutils.dir/rule

.PHONY : imageutils

# fast build rule for target.
imageutils/fast:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/imageutils.dir/build.make utils.out/CMakeFiles/imageutils.dir/build
.PHONY : imageutils/fast

# Convenience name for target.
utils.out/CMakeFiles/imagedrawing.dir/rule:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f CMakeFiles/Makefile2 utils.out/CMakeFiles/imagedrawing.dir/rule
.PHONY : utils.out/CMakeFiles/imagedrawing.dir/rule

# Convenience name for target.
imagedrawing: utils.out/CMakeFiles/imagedrawing.dir/rule

.PHONY : imagedrawing

# fast build rule for target.
imagedrawing/fast:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/imagedrawing.dir/build.make utils.out/CMakeFiles/imagedrawing.dir/build
.PHONY : imagedrawing/fast

# Convenience name for target.
utils.out/CMakeFiles/fileutils.dir/rule:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f CMakeFiles/Makefile2 utils.out/CMakeFiles/fileutils.dir/rule
.PHONY : utils.out/CMakeFiles/fileutils.dir/rule

# Convenience name for target.
fileutils: utils.out/CMakeFiles/fileutils.dir/rule

.PHONY : fileutils

# fast build rule for target.
fileutils/fast:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/fileutils.dir/build.make utils.out/CMakeFiles/fileutils.dir/build
.PHONY : fileutils/fast

audio_utils.o: audio_utils.c.o

.PHONY : audio_utils.o

# target to build an object file
audio_utils.c.o:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/audioutils.dir/build.make utils.out/CMakeFiles/audioutils.dir/audio_utils.c.o
.PHONY : audio_utils.c.o

audio_utils.i: audio_utils.c.i

.PHONY : audio_utils.i

# target to preprocess a source file
audio_utils.c.i:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/audioutils.dir/build.make utils.out/CMakeFiles/audioutils.dir/audio_utils.c.i
.PHONY : audio_utils.c.i

audio_utils.s: audio_utils.c.s

.PHONY : audio_utils.s

# target to generate assembly for a file
audio_utils.c.s:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/audioutils.dir/build.make utils.out/CMakeFiles/audioutils.dir/audio_utils.c.s
.PHONY : audio_utils.c.s

file_utils.o: file_utils.c.o

.PHONY : file_utils.o

# target to build an object file
file_utils.c.o:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/fileutils.dir/build.make utils.out/CMakeFiles/fileutils.dir/file_utils.c.o
.PHONY : file_utils.c.o

file_utils.i: file_utils.c.i

.PHONY : file_utils.i

# target to preprocess a source file
file_utils.c.i:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/fileutils.dir/build.make utils.out/CMakeFiles/fileutils.dir/file_utils.c.i
.PHONY : file_utils.c.i

file_utils.s: file_utils.c.s

.PHONY : file_utils.s

# target to generate assembly for a file
file_utils.c.s:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/fileutils.dir/build.make utils.out/CMakeFiles/fileutils.dir/file_utils.c.s
.PHONY : file_utils.c.s

image_drawing.o: image_drawing.c.o

.PHONY : image_drawing.o

# target to build an object file
image_drawing.c.o:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/imagedrawing.dir/build.make utils.out/CMakeFiles/imagedrawing.dir/image_drawing.c.o
.PHONY : image_drawing.c.o

image_drawing.i: image_drawing.c.i

.PHONY : image_drawing.i

# target to preprocess a source file
image_drawing.c.i:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/imagedrawing.dir/build.make utils.out/CMakeFiles/imagedrawing.dir/image_drawing.c.i
.PHONY : image_drawing.c.i

image_drawing.s: image_drawing.c.s

.PHONY : image_drawing.s

# target to generate assembly for a file
image_drawing.c.s:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/imagedrawing.dir/build.make utils.out/CMakeFiles/imagedrawing.dir/image_drawing.c.s
.PHONY : image_drawing.c.s

image_utils.o: image_utils.c.o

.PHONY : image_utils.o

# target to build an object file
image_utils.c.o:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/imageutils.dir/build.make utils.out/CMakeFiles/imageutils.dir/image_utils.c.o
.PHONY : image_utils.c.o

image_utils.i: image_utils.c.i

.PHONY : image_utils.i

# target to preprocess a source file
image_utils.c.i:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/imageutils.dir/build.make utils.out/CMakeFiles/imageutils.dir/image_utils.c.i
.PHONY : image_utils.c.i

image_utils.s: image_utils.c.s

.PHONY : image_utils.s

# target to generate assembly for a file
image_utils.c.s:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(MAKE) -f utils.out/CMakeFiles/imageutils.dir/build.make utils.out/CMakeFiles/imageutils.dir/image_utils.c.s
.PHONY : image_utils.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... audioutils"
	@echo "... imageutils"
	@echo "... imagedrawing"
	@echo "... fileutils"
	@echo "... audio_utils.o"
	@echo "... audio_utils.i"
	@echo "... audio_utils.s"
	@echo "... file_utils.o"
	@echo "... file_utils.i"
	@echo "... file_utils.s"
	@echo "... image_drawing.o"
	@echo "... image_drawing.i"
	@echo "... image_drawing.s"
	@echo "... image_utils.o"
	@echo "... image_utils.i"
	@echo "... image_utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

