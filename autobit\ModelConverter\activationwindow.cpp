#include "activationwindow.h"
#include "HardwareFingerprint.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QMessageBox>
#include <QApplication>
#include <QDesktopWidget>
#include <QScreen>
#include <QDebug>

ActivationWindow::ActivationWindow(QWidget *parent)
    : QWidget(parent), convWindow(nullptr), isDebugMode(false), isActivating(false)
{
    // 初始化许可证管理器
    licenseManager = new LicenseManager(this);
    connect(licenseManager, &LicenseManager::activationFinished,
            this, &ActivationWindow::onActivationFinished);
    connect(licenseManager, &LicenseManager::licenseStatusChanged,
            this, &ActivationWindow::onLicenseStatusChanged);

    setupUI();
    setupStyles();
    setupAnimations();

    // 检查现有许可证状态
    LicenseManager::LicenseStatus status = licenseManager->checkLicenseStatus();
    if (status == LicenseManager::Valid) {
        setStatus("检测到有效许可证，可直接使用", "success");
        activateButton->setText("进入主界面");
    }
}

void ActivationWindow::setupUI()
{
    setWindowTitle("AutoBit 模型转换工具 - 激活");
    setFixedSize(500, 650);

    // 居中显示
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);

    // 主框架
    mainFrame = new QFrame(this);
    mainFrame->setObjectName("mainFrame");

    // 标题和Logo
    titleLabel = new QLabel("AutoBit 模型转换工具", this);
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);

    logoLabel = new QLabel("🚀", this);
    logoLabel->setObjectName("logoLabel");
    logoLabel->setAlignment(Qt::AlignCenter);

    // 激活码输入
    QGroupBox *activationGroup = new QGroupBox("许可证激活", this);
    activationGroup->setObjectName("activationGroup");

    QLabel *licenseLabel = new QLabel("激活码:", this);
    licenseEdit = new QLineEdit(this);
    licenseEdit->setObjectName("licenseEdit");
    licenseEdit->setPlaceholderText("请输入激活码 (格式: XXXX-XXXX-XXXX-XXXX)");

    // 调试模式
    debugModeCheckBox = new QCheckBox("调试模式", this);
    debugModeCheckBox->setObjectName("debugModeCheckBox");
    connect(debugModeCheckBox, &QCheckBox::toggled, this, &ActivationWindow::onDebugModeChanged);

    debugDeviceCombo = new QComboBox(this);
    debugDeviceCombo->setObjectName("debugDeviceCombo");
    debugDeviceCombo->addItems({
        "测试设备-001 (50bf70582c5ea2ac7502656f8cfb522e)",
        "测试设备-002 (a1b2c3d4e5f6789012345678901234ab)",
        "测试设备-003 (1234567890abcdef1234567890abcdef)"
    });
    debugDeviceCombo->setVisible(false);

    // 设备信息
    deviceInfoLabel = new QLabel("设备ID: 正在获取...", this);
    deviceInfoLabel->setObjectName("deviceInfoLabel");
    deviceInfoLabel->setWordWrap(true);

    deviceInfoButton = new QPushButton("显示设备信息", this);
    deviceInfoButton->setObjectName("deviceInfoButton");
    connect(deviceInfoButton, &QPushButton::clicked, this, &ActivationWindow::showDeviceInfo);

    // 激活按钮和进度
    activateButton = new QPushButton("激活", this);
    activateButton->setObjectName("activateButton");
    connect(activateButton, &QPushButton::clicked, this, &ActivationWindow::onActivateClicked);

    progressBar = new QProgressBar(this);
    progressBar->setObjectName("progressBar");
    progressBar->setVisible(false);
    progressBar->setRange(0, 0); // 无限进度条

    loadingLabel = new QLabel(this);
    loadingLabel->setObjectName("loadingLabel");
    loadingLabel->setAlignment(Qt::AlignCenter);
    loadingLabel->setVisible(false);

    // 状态标签
    statusLabel = new QLabel("请输入激活码进行激活", this);
    statusLabel->setObjectName("statusLabel");
    statusLabel->setAlignment(Qt::AlignCenter);
    statusLabel->setWordWrap(true);

    // 布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(30, 30, 30, 30);
    mainLayout->setSpacing(20);

    // 标题区域
    QVBoxLayout *headerLayout = new QVBoxLayout();
    headerLayout->addWidget(logoLabel);
    headerLayout->addWidget(titleLabel);
    headerLayout->setSpacing(10);

    // 激活区域
    QVBoxLayout *activationLayout = new QVBoxLayout(activationGroup);
    activationLayout->addWidget(licenseLabel);
    activationLayout->addWidget(licenseEdit);
    activationLayout->addWidget(debugModeCheckBox);
    activationLayout->addWidget(debugDeviceCombo);
    activationLayout->setSpacing(10);

    // 设备信息区域
    QHBoxLayout *deviceLayout = new QHBoxLayout();
    deviceLayout->addWidget(deviceInfoLabel, 1);
    deviceLayout->addWidget(deviceInfoButton);

    // 按钮区域
    QVBoxLayout *buttonLayout = new QVBoxLayout();
    buttonLayout->addWidget(activateButton);
    buttonLayout->addWidget(progressBar);
    buttonLayout->addWidget(loadingLabel);
    buttonLayout->setSpacing(10);

    // 主布局
    mainLayout->addLayout(headerLayout);
    mainLayout->addWidget(activationGroup);
    mainLayout->addLayout(deviceLayout);
    mainLayout->addLayout(buttonLayout);
    mainLayout->addWidget(statusLabel);
    mainLayout->addStretch();

    // 获取并显示设备ID
    QString deviceId = getCurrentDeviceId();
    deviceInfoLabel->setText(QString("设备ID: %1").arg(deviceId.left(16) + "..."));
}

void ActivationWindow::onActivateClicked()
{
    if (isActivating) {
        return;
    }

    // 检查是否已有有效许可证
    LicenseManager::LicenseStatus status = licenseManager->checkLicenseStatus();
    if (status == LicenseManager::Valid) {
        // 直接进入主界面
        showActivationSuccess();
        return;
    }

    QString activationCode = licenseEdit->text().trimmed().toUpper();
    if (activationCode.isEmpty()) {
        setStatus("请输入激活码", "error");
        return;
    }

    // 验证激活码格式
    if (!LicenseManager::isValidActivationCodeFormat(activationCode)) {
        setStatus("激活码格式无效，请输入正确格式: XXXX-XXXX-XXXX-XXXX", "error");
        return;
    }

    // 开始激活
    isActivating = true;
    activateButton->setEnabled(false);
    activateButton->setText("激活中...");
    progressBar->setVisible(true);
    loadingLabel->setText("正在验证激活码...");
    loadingLabel->setVisible(true);

    setStatus("正在连接激活服务器...", "info");

    // 设置调试模式
    licenseManager->setDebugMode(isDebugMode);

    // 开始激活
    licenseManager->activateLicense(activationCode);
}

void ActivationWindow::onDebugModeChanged(bool enabled)
{
    isDebugMode = enabled;
    debugDeviceCombo->setVisible(enabled);

    if (enabled) {
        setStatus("调试模式已启用，可使用测试激活码", "info");
        licenseEdit->setPlaceholderText("调试模式: TEST-1234-5678-ABCD 或 E76G-JEQR-EQRA-T7ZW");
    } else {
        setStatus("生产模式，需要有效的激活码", "info");
        licenseEdit->setPlaceholderText("请输入激活码 (格式: XXXX-XXXX-XXXX-XXXX)");
    }
}

void ActivationWindow::showDeviceInfo()
{
    QString deviceId = getCurrentDeviceId();
    QString info = QString(
        "设备信息:\n\n"
        "设备ID: %1\n"
        "CPU信息: %2\n"
        "MAC地址: %3\n"
        "系统信息: %4\n\n"
        "注意: 设备ID用于硬件绑定，请妥善保管"
    ).arg(deviceId)
     .arg(HardwareFingerprint::getCpuInfo())
     .arg(HardwareFingerprint::getMacAddress())
     .arg(HardwareFingerprint::getSystemInfo());

    QMessageBox::information(this, "设备信息", info);
}

void ActivationWindow::onActivationFinished(LicenseManager::ActivationResult result, const QString &message)
{
    isActivating = false;
    activateButton->setEnabled(true);
    progressBar->setVisible(false);
    loadingLabel->setVisible(false);

    switch (result) {
    case LicenseManager::Success:
    case LicenseManager::DebugModeSuccess:
        setStatus("激活成功！", "success");
        showActivationSuccess();
        break;

    case LicenseManager::InvalidCode:
        activateButton->setText("激活");
        setStatus("激活码无效: " + message, "error");
        break;

    case LicenseManager::NetworkError:
        activateButton->setText("激活");
        setStatus("网络连接失败: " + message, "error");
        break;

    case LicenseManager::HardwareMismatch:
        activateButton->setText("激活");
        setStatus("硬件不匹配: " + message, "error");
        break;

    case LicenseManager::ServerError:
        activateButton->setText("激活");
        setStatus("服务器错误: " + message, "error");
        break;

    case LicenseManager::AlreadyActivated:
        activateButton->setText("激活");
        setStatus("设备已激活: " + message, "warning");
        break;

    case LicenseManager::ExpiredLicense:
        activateButton->setText("激活");
        setStatus("许可证已过期: " + message, "error");
        break;
    }
}

void ActivationWindow::onLicenseStatusChanged(bool isValid)
{
    if (isValid) {
        activateButton->setText("进入主界面");
    } else {
        activateButton->setText("激活");
    }
}

void ActivationWindow::showActivationSuccess()
{
    setStatus("激活成功，正在启动主界面...", "success");

    // 延迟启动主界面
    QTimer::singleShot(1500, this, [this]() {
        convWindow = new ConversionWindow();
        convWindow->show();
        this->close();
    });
}

void ActivationWindow::showActivationError(const QString &error)
{
    setStatus(error, "error");
    QMessageBox::warning(this, "激活失败", error);
}

QString ActivationWindow::getCurrentDeviceId()
{
    if (isDebugMode && debugDeviceCombo->isVisible()) {
        QString selected = debugDeviceCombo->currentText();
        // 提取括号中的设备ID
        int start = selected.indexOf('(') + 1;
        int end = selected.indexOf(')');
        if (start > 0 && end > start) {
            return selected.mid(start, end - start);
        }
    }

    return licenseManager->getCurrentDeviceId();
}

void ActivationWindow::setupStyles()
{
    setStyleSheet(R"(
        QWidget {
            background-color: #f5f5f5;
            font-family: "Microsoft YaHei", "SimHei", sans-serif;
        }

        #mainFrame {
            background-color: white;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }

        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin: 10px 0;
        }

        #logoLabel {
            font-size: 48px;
            margin: 10px 0;
        }

        QGroupBox {
            font-size: 14px;
            font-weight: bold;
            color: #34495e;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            background-color: white;
        }

        #licenseEdit {
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
        }

        #licenseEdit:focus {
            border-color: #3498db;
            outline: none;
        }

        #activateButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            min-height: 20px;
        }

        #activateButton:hover {
            background-color: #2980b9;
        }

        #activateButton:pressed {
            background-color: #21618c;
        }

        #activateButton:disabled {
            background-color: #95a5a6;
        }

        #deviceInfoButton {
            background-color: #95a5a6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
        }

        #deviceInfoButton:hover {
            background-color: #7f8c8d;
        }

        #statusLabel {
            font-size: 14px;
            padding: 10px;
            border-radius: 6px;
            background-color: #ecf0f1;
            color: #2c3e50;
        }

        #statusLabel[type="success"] {
            background-color: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
        }

        #statusLabel[type="error"] {
            background-color: #fadbd8;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }

        #statusLabel[type="warning"] {
            background-color: #fef9e7;
            color: #f39c12;
            border: 1px solid #f39c12;
        }

        #deviceInfoLabel {
            font-size: 12px;
            color: #7f8c8d;
            font-family: monospace;
        }

        #debugModeCheckBox {
            font-size: 12px;
            color: #e67e22;
        }

        #progressBar {
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            background-color: #ecf0f1;
        }

        #progressBar::chunk {
            background-color: #3498db;
            border-radius: 3px;
        }

        #loadingLabel {
            font-size: 12px;
            color: #7f8c8d;
            font-style: italic;
        }
    )");
}

void ActivationWindow::setupAnimations()
{
    // 状态标签动画
    statusAnimation = new QPropertyAnimation(statusLabel, "geometry", this);
    statusAnimation->setDuration(300);
    statusAnimation->setEasingCurve(QEasingCurve::OutCubic);

    // 按钮动画
    buttonAnimation = new QPropertyAnimation(activateButton, "geometry", this);
    buttonAnimation->setDuration(200);
    buttonAnimation->setEasingCurve(QEasingCurve::OutCubic);

    // 状态定时器
    statusTimer = new QTimer(this);
    statusTimer->setSingleShot(true);
    connect(statusTimer, &QTimer::timeout, [this]() {
        if (!isActivating) {
            setStatus("请输入激活码进行激活", "info");
        }
    });
}

void ActivationWindow::setStatus(const QString &message, const QString &type)
{
    statusLabel->setText(message);
    statusLabel->setProperty("type", type);
    statusLabel->style()->unpolish(statusLabel);
    statusLabel->style()->polish(statusLabel);

    // 重置定时器
    if (type != "success" && type != "error") {
        statusTimer->start(5000); // 5秒后恢复默认状态
    }

    qDebug() << "[激活窗口] 状态更新:" << type << "-" << message;
}
