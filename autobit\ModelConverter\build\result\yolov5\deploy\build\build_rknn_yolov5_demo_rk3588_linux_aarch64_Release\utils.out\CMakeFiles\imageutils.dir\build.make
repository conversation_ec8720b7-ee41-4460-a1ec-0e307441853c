# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspace/yolo-rk3588/yolov5/cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release

# Include any dependencies generated for this target.
include utils.out/CMakeFiles/imageutils.dir/depend.make

# Include the progress variables for this target.
include utils.out/CMakeFiles/imageutils.dir/progress.make

# Include the compile flags for this target's objects.
include utils.out/CMakeFiles/imageutils.dir/flags.make

utils.out/CMakeFiles/imageutils.dir/image_utils.c.o: utils.out/CMakeFiles/imageutils.dir/flags.make
utils.out/CMakeFiles/imageutils.dir/image_utils.c.o: /workspace/yolo-rk3588/utils/image_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object utils.out/CMakeFiles/imageutils.dir/image_utils.c.o"
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out && /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/imageutils.dir/image_utils.c.o   -c /workspace/yolo-rk3588/utils/image_utils.c

utils.out/CMakeFiles/imageutils.dir/image_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/imageutils.dir/image_utils.c.i"
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out && /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /workspace/yolo-rk3588/utils/image_utils.c > CMakeFiles/imageutils.dir/image_utils.c.i

utils.out/CMakeFiles/imageutils.dir/image_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/imageutils.dir/image_utils.c.s"
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out && /workspace/yolo-rk3588/tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /workspace/yolo-rk3588/utils/image_utils.c -o CMakeFiles/imageutils.dir/image_utils.c.s

# Object files for target imageutils
imageutils_OBJECTS = \
"CMakeFiles/imageutils.dir/image_utils.c.o"

# External object files for target imageutils
imageutils_EXTERNAL_OBJECTS =

utils.out/libimageutils.a: utils.out/CMakeFiles/imageutils.dir/image_utils.c.o
utils.out/libimageutils.a: utils.out/CMakeFiles/imageutils.dir/build.make
utils.out/libimageutils.a: utils.out/CMakeFiles/imageutils.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library libimageutils.a"
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out && $(CMAKE_COMMAND) -P CMakeFiles/imageutils.dir/cmake_clean_target.cmake
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/imageutils.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
utils.out/CMakeFiles/imageutils.dir/build: utils.out/libimageutils.a

.PHONY : utils.out/CMakeFiles/imageutils.dir/build

utils.out/CMakeFiles/imageutils.dir/clean:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out && $(CMAKE_COMMAND) -P CMakeFiles/imageutils.dir/cmake_clean.cmake
.PHONY : utils.out/CMakeFiles/imageutils.dir/clean

utils.out/CMakeFiles/imageutils.dir/depend:
	cd /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /workspace/yolo-rk3588/yolov5/cpp /workspace/yolo-rk3588/utils /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out /workspace/yolo-rk3588/result/yolov5/deploy/build/build_rknn_yolov5_demo_rk3588_linux_aarch64_Release/utils.out/CMakeFiles/imageutils.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : utils.out/CMakeFiles/imageutils.dir/depend

