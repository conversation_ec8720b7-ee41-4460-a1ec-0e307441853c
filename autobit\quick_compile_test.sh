#!/bin/bash
# 快速编译测试脚本 - 用于验证编译错误修复

set -e

echo "=== AutoBit 快速编译测试 ==="

# 进入项目目录
cd "$(dirname "$0")/ModelConverter"

# 设置Qt环境（如果存在）
QT_DIR="/opt/Qt5.12.6/5.12.6/gcc_64"
if [ -d "$QT_DIR" ]; then
    export PATH="$QT_DIR/bin:$PATH"
    export LD_LIBRARY_PATH="$QT_DIR/lib:$LD_LIBRARY_PATH"
    echo "✅ 使用Qt 5.12.6: $QT_DIR"
else
    echo "⚠️ 使用系统Qt"
fi

# 检查qmake
if ! command -v qmake &> /dev/null; then
    echo "❌ qmake未找到，请确保Qt已正确安装"
    exit 1
fi

echo "Qt版本: $(qmake --version | grep Qt)"

# 清理之前的编译
echo "清理之前的编译文件..."
cd build
rm -f *.o moc_* qrc_* Makefile ModelConverter

# 重新生成Makefile
echo "生成Makefile..."
qmake ../ModelConverter.pro

# 尝试编译
echo "开始编译..."
make -j$(nproc) 2>&1 | tee compile.log

# 检查编译结果
if [ -f "ModelConverter" ]; then
    echo "✅ 编译成功！"
    echo "可执行文件: $(pwd)/ModelConverter"
    
    # 检查依赖
    echo "检查依赖库..."
    ldd ./ModelConverter | grep "not found" && echo "⚠️ 发现缺失依赖" || echo "✅ 依赖检查通过"
    
else
    echo "❌ 编译失败，请检查错误信息"
    echo "编译日志已保存到: $(pwd)/compile.log"
    exit 1
fi

echo "=== 编译测试完成 ==="
