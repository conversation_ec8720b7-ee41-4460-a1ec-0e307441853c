# 指定要使用的 Qt 安装前缀
QMAKE_PREFIX_PATH = /media/rhs/t161/qt512/5.12.6/gcc_64

QT       += core gui widgets
TARGET   = ModelConverter
CONFIG   += c++17

# 禁用 sized-deallocation，防止库里对 operator delete(void*,size_t) 的依赖
QMAKE_CXXFLAGS += -fno-sized-deallocation

# 动态库导入声明
DEFINES += SF_CORE_LS_EXPORT=Q_DECL_IMPORT

# 强制链接 Qt5.12.6 的库
LIBS += -L$$QMAKE_PREFIX_PATH/lib \
        -lQt5Widgets \
        -lQt5Gui \
        -lQt5Core

# 链接第三方的 sf-core-ls 动态库（.so 放在项目根目录）
LIBS += -L$$PWD -lsf-core-ls

# 设置运行时 rpath，让可执行加载时优先在上述目录寻找 .so
QMAKE_RPATHDIR += $$QMAKE_PREFIX_PATH/lib \
                  $$PWD

# 资源、源码、头文件列表
RESOURCES += ModelConverter.qrc

SOURCES  += main.cpp \
            activationwindow.cpp \
            conversionwindow.cpp \
            sized_delete.cpp

HEADERS  += activationwindow.h \
            conversionwindow.h
