# Qt项目配置 - Ubuntu WSL2环境（无许可证库版本）
QT       += core gui widgets
TARGET   = ModelConverter
CONFIG   += c++17

# 禁用 sized-deallocation
QMAKE_CXXFLAGS += -fno-sized-deallocation

# 不使用许可证库相关定义
# DEFINES += SF_CORE_LS_EXPORT=Q_DECL_IMPORT

# 不链接许可证库
# LIBS += -L$$PWD -lsf-core-ls

# 设置运行时 rpath
QMAKE_RPATHDIR += $$PWD

# 源码和头文件
SOURCES  += main.cpp \
            activationwindow.cpp \
            conversionwindow.cpp \
            sized_delete.cpp

HEADERS  += activationwindow.h \
            conversionwindow.h
