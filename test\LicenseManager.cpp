#include "LicenseManager.h"
#include "HardwareFingerprint.h"
#include <QNetworkRequest>
#include <QUrlQuery>
#include <QJsonParseError>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QRandomGenerator>
#include <QRegularExpression>

// 服务器配置
const QString LicenseManager::SERVER_URL = "https://api.shifang.co";
const QString LicenseManager::ACTIVATION_ENDPOINT = "/license/activate";
const QString LicenseManager::VERIFICATION_ENDPOINT = "/license/verify";

// 调试模式配置
const QStringList LicenseManager::DEBUG_VALID_CODES = {
    "TEST-1234-5678-ABCD",
    "DEBUG-2024-0629-DEMO",
    "DEV-DIAGNOSIS-APP-RK",
    "RK3588-MEDICAL-TEST",
    "E76G-JEQR-EQRA-T7ZW"
};
const QString LicenseManager::DEBUG_DEVICE_PREFIX = "DEBUG-RK3588-";

LicenseManager::LicenseManager(QObject *parent)
    : QObject(parent)
    , networkManager(new QNetworkAccessManager(this))
    , debugMode(false)
{
    // 初始化设置存储
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir().mkpath(configPath);
    settings = new QSettings(configPath + "/diagnosis_license.conf", QSettings::IniFormat, this);
    
    qDebug() << "[许可证管理器] 初始化完成，配置路径:" << configPath;
}

void LicenseManager::setDebugMode(bool enabled)
{
    debugMode = enabled;
    qDebug() << "[许可证管理器] 调试模式:" << (enabled ? "启用" : "禁用");
}

LicenseManager::LicenseStatus LicenseManager::checkLicenseStatus()
{
    if (debugMode) {
        // 调试模式下总是返回有效状态
        return Valid;
    }
    
    QJsonObject license = loadLicense();
    if (license.isEmpty()) {
        return NotActivated;
    }
    
    // 检查许可证完整性
    if (!verifyLicenseIntegrity(license)) {
        qDebug() << "[许可证管理器] 许可证完整性验证失败";
        return Invalid;
    }
    
    // 检查过期时间
    QString expiryStr = license["expiry"].toString();
    if (!expiryStr.isEmpty()) {
        QDateTime expiry = QDateTime::fromString(expiryStr, Qt::ISODate);
        if (expiry.isValid() && expiry < QDateTime::currentDateTime()) {
            qDebug() << "[许可证管理器] 许可证已过期:" << expiryStr;
            return Expired;
        }
    }
    
    // 检查硬件绑定
    QString storedDeviceId = license["device_id"].toString();
    QString currentDeviceId = getCurrentDeviceId();
    if (!HardwareFingerprint::verifyFingerprint(storedDeviceId, currentDeviceId)) {
        qDebug() << "[许可证管理器] 硬件指纹不匹配";
        return Invalid;
    }
    
    return Valid;
}

void LicenseManager::activateLicense(const QString &activationCode, const QString &deviceId)
{
    currentActivationCode = activationCode.trimmed().toUpper();
    currentDeviceId = deviceId.isEmpty() ? getCurrentDeviceId() : deviceId;
    
    qDebug() << "[许可证管理器] 开始激活:";
    qDebug() << "  激活码:" << currentActivationCode;
    qDebug() << "  设备ID:" << currentDeviceId;
    qDebug() << "  调试模式:" << debugMode;
    
    // 验证激活码格式
    if (!isValidActivationCodeFormat(currentActivationCode)) {
        emit activationFinished(InvalidCode, "激活码格式无效");
        return;
    }
    
    if (debugMode) {
        activateDebug(currentActivationCode);
    } else {
        activateProduction(currentActivationCode, currentDeviceId);
    }
}

QString LicenseManager::getCurrentDeviceId()
{
    if (debugMode) {
        // 调试模式使用固定的设备ID
        return DEBUG_DEVICE_PREFIX + "50bf70582c5ea2ac7502656f8cfb522e";
    }
    
    return HardwareFingerprint::getDeviceFingerprint();
}

QJsonObject LicenseManager::getLicenseInfo()
{
    return loadLicense();
}

void LicenseManager::clearLicense()
{
    settings->clear();
    settings->sync();
    qDebug() << "[许可证管理器] 已清除本地许可证";
    emit licenseStatusChanged(false);
}

bool LicenseManager::isValidActivationCodeFormat(const QString &code)
{
    // 激活码格式：XXXX-XXXX-XXXX-XXXX（4组4位字符，用连字符分隔）
    QRegularExpression regex("^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$");
    return regex.match(code).hasMatch();
}

void LicenseManager::activateProduction(const QString &activationCode, const QString &deviceId)
{
    qDebug() << "[许可证管理器] 生产模式激活";

    // 构建请求
    QNetworkRequest request;
    request.setUrl(QUrl(SERVER_URL + ACTIVATION_ENDPOINT));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // 构建请求数据
    QJsonObject requestData;
    requestData["activation_code"] = activationCode;
    requestData["device_id"] = deviceId;
    requestData["product_id"] = "40202";  // 诊断应用产品ID
    requestData["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(requestData);
    QByteArray data = doc.toJson();

    qDebug() << "[许可证管理器] 发送激活请求:" << data;

    // 发送请求
    QNetworkReply *reply = networkManager->post(request, data);
    connect(reply, &QNetworkReply::finished, this, &LicenseManager::onActivationReplyFinished);
    connect(reply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::error),
            this, &LicenseManager::onNetworkError);
}

void LicenseManager::activateDebug(const QString &activationCode)
{
    qDebug() << "[许可证管理器] 调试模式激活";

    // 检查是否是有效的调试激活码
    if (!DEBUG_VALID_CODES.contains(activationCode)) {
        emit activationFinished(InvalidCode, "调试模式：激活码无效");
        return;
    }

    // 创建调试许可证
    QJsonObject debugLicense;
    debugLicense["activation_code"] = activationCode;
    debugLicense["device_id"] = currentDeviceId;
    debugLicense["product_id"] = "40202";
    debugLicense["activated_at"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    debugLicense["expiry"] = QDateTime::currentDateTime().addYears(1).toString(Qt::ISODate);
    debugLicense["debug_mode"] = true;
    debugLicense["status"] = "active";

    // 保存调试许可证
    saveLicense(debugLicense);

    emit activationFinished(DebugModeSuccess, "调试模式激活成功");
    emit licenseStatusChanged(true);
}

void LicenseManager::onActivationReplyFinished()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    reply->deleteLater();

    if (reply->error() != QNetworkReply::NoError) {
        qDebug() << "[许可证管理器] 网络错误:" << reply->errorString();
        emit activationFinished(NetworkError, "网络连接失败: " + reply->errorString());
        return;
    }

    // 解析响应
    QByteArray responseData = reply->readAll();
    qDebug() << "[许可证管理器] 服务器响应:" << responseData;

    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(responseData, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        qDebug() << "[许可证管理器] JSON解析错误:" << parseError.errorString();
        emit activationFinished(ServerError, "服务器响应格式错误");
        return;
    }

    QJsonObject response = doc.object();
    QString status = response["status"].toString();
    QString message = response["message"].toString();

    if (status == "success") {
        // 激活成功，保存许可证
        QJsonObject licenseData = response["license"].toObject();
        licenseData["device_id"] = currentDeviceId;
        licenseData["activated_at"] = QDateTime::currentDateTime().toString(Qt::ISODate);

        saveLicense(licenseData);

        emit activationFinished(Success, "激活成功");
        emit licenseStatusChanged(true);
    } else {
        // 激活失败
        ActivationResult result = InvalidCode;
        if (message.contains("hardware", Qt::CaseInsensitive)) {
            result = HardwareMismatch;
        } else if (message.contains("expired", Qt::CaseInsensitive)) {
            result = ExpiredLicense;
        } else if (message.contains("activated", Qt::CaseInsensitive)) {
            result = AlreadyActivated;
        }

        emit activationFinished(result, message);
    }
}

void LicenseManager::onNetworkError(QNetworkReply::NetworkError error)
{
    Q_UNUSED(error)
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (reply) {
        qDebug() << "[许可证管理器] 网络错误:" << reply->errorString();
        emit activationFinished(NetworkError, "网络连接失败: " + reply->errorString());
    }
}

void LicenseManager::saveLicense(const QJsonObject &licenseData)
{
    QString deviceId = licenseData["device_id"].toString();
    QString encryptionKey = generateEncryptionKey(deviceId);

    // 序列化许可证数据
    QJsonDocument doc(licenseData);
    QString jsonString = doc.toJson(QJsonDocument::Compact);

    // 加密许可证数据
    QString encryptedData = encryptData(jsonString, encryptionKey);

    // 保存到本地
    settings->setValue("license/data", encryptedData);
    settings->setValue("license/checksum", generateMD5(jsonString + deviceId));
    settings->sync();

    qDebug() << "[许可证管理器] 许可证已保存到本地";
}

QJsonObject LicenseManager::loadLicense()
{
    QString encryptedData = settings->value("license/data").toString();
    if (encryptedData.isEmpty()) {
        return QJsonObject();
    }

    QString deviceId = getCurrentDeviceId();
    QString encryptionKey = generateEncryptionKey(deviceId);

    // 解密许可证数据
    QString jsonString = decryptData(encryptedData, encryptionKey);
    if (jsonString.isEmpty()) {
        qDebug() << "[许可证管理器] 许可证解密失败";
        return QJsonObject();
    }

    // 解析JSON
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8(), &parseError);
    if (parseError.error != QJsonParseError::NoError) {
        qDebug() << "[许可证管理器] 许可证JSON解析失败:" << parseError.errorString();
        return QJsonObject();
    }

    return doc.object();
}

QString LicenseManager::encryptData(const QString &data, const QString &key)
{
    // 简单的XOR加密（生产环境应使用更强的加密算法）
    QByteArray dataBytes = data.toUtf8();
    QByteArray keyBytes = key.toUtf8();
    QByteArray encrypted;

    for (int i = 0; i < dataBytes.size(); ++i) {
        encrypted.append(dataBytes[i] ^ keyBytes[i % keyBytes.size()]);
    }

    return encrypted.toBase64();
}

QString LicenseManager::decryptData(const QString &encryptedData, const QString &key)
{
    QByteArray encrypted = QByteArray::fromBase64(encryptedData.toUtf8());
    QByteArray keyBytes = key.toUtf8();
    QByteArray decrypted;

    for (int i = 0; i < encrypted.size(); ++i) {
        decrypted.append(encrypted[i] ^ keyBytes[i % keyBytes.size()]);
    }

    return QString::fromUtf8(decrypted);
}

QString LicenseManager::generateEncryptionKey(const QString &deviceId)
{
    // 基于设备ID生成加密密钥
    QString salt = "DiagnosisAppRK3588_2024";
    return generateMD5(deviceId + salt);
}

QString LicenseManager::generateMD5(const QString &input)
{
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(input.toUtf8());
    return hash.result().toHex();
}

bool LicenseManager::verifyLicenseIntegrity(const QJsonObject &licenseData)
{
    QString storedChecksum = settings->value("license/checksum").toString();
    if (storedChecksum.isEmpty()) {
        return false;
    }

    QString deviceId = licenseData["device_id"].toString();
    QJsonDocument doc(licenseData);
    QString jsonString = doc.toJson(QJsonDocument::Compact);
    QString calculatedChecksum = generateMD5(jsonString + deviceId);

    return storedChecksum == calculatedChecksum;
}
