[34m[1mexport: [0mdata=yolov5/data/coco128.yaml, weights=['/workspace/yolo-rk3588/models/yolov5s.pt'], result=/workspace/yolo-rk3588/result/yolov5/yolov5.onnx, imgsz=[640, 640], batch_size=1, device=cpu, half=False, inplace=False, keras=False, optimize=False, int8=False, dynamic=False, simplify=False, opset=12, verbose=False, workspace=4, nms=False, agnostic_nms=False, topk_per_class=100, topk_all=100, iou_thres=0.45, conf_thres=0.25, include=['onnx'], rknpu=True
YOLOv5 🚀 v4.0-1657-gd25a075 Python-3.8.13 torch-1.10.1+cu102 CPU

Fusing layers... 
YOLOv5s summary: 213 layers, 7225885 parameters, 0 gradients, 16.4 GFLOPs

[34m[1mPyTorch:[0m starting from /workspace/yolo-rk3588/models/yolov5s.pt with output shape (1, 255, 80, 80) (14.1 MB)

[34m[1mONNX:[0m starting export with onnx 1.17.0...
[34m[1mONNX:[0m export success ✅ 2.1s, saved as /workspace/yolo-rk3588/result/yolov5/yolov5.onnx (27.6 MB)

Export complete (2.7s)
Results saved to [1m/workspace/yolo-rk3588/models[0m
Detect:          python detect.py --weights /workspace/yolo-rk3588/result/yolov5/yolov5.onnx 
Validate:        python val.py --weights /workspace/yolo-rk3588/result/yolov5/yolov5.onnx 
PyTorch Hub:     model = torch.hub.load('ultralytics/yolov5', 'custom', '/workspace/yolo-rk3588/result/yolov5/yolov5.onnx')  
Visualize:       https://netron.app
---> save anchors for RKNN
[[10.0, 13.0], [16.0, 30.0], [33.0, 23.0], [30.0, 61.0], [62.0, 45.0], [59.0, 119.0], [116.0, 90.0], [156.0, 198.0], [373.0, 326.0]]
export detect model for RKNPU
