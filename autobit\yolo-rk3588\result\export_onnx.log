Ultralytics YOLOv8.2.82 🚀 Python-3.8.13 torch-1.10.1+cu102 CPU (unknown)
YOLOv8n summary (fused): 168 layers, 3,151,904 parameters, 0 gradients, 8.7 GFLOPs

[34m[1mPyTorch:[0m starting from 'models/yolov8n.pt' with input shape (16, 3, 640, 640) BCHW and output shape(s) ((16, 64, 80, 80), (16, 80, 80, 80), (16, 1, 80, 80), (16, 64, 40, 40), (16, 80, 40, 40), (16, 1, 40, 40), (16, 64, 20, 20), (16, 80, 20, 20), (16, 1, 20, 20)) (6.2 MB)

[34m[1mRKNN:[0m starting export with torch 1.10.1+cu102...

[34m[1mRKNN:[0m feed /workspace/yolo-rk3588/result/yolov8/yolov8.onnx to RKNN-Toolkit or RKNN-Toolkit2 to generate RKNN model.
Refer https://github.com/airockchip/rknn_model_zoo/tree/main/models/CV/object_detection/yolo
[34m[1mRKNN:[0m export success ✅ 1.3s, saved as '/workspace/yolo-rk3588/result/yolov8/yolov8.onnx' (12.0 MB)

Export complete (3.7s)
Results saved to [1m/workspace/yolo-rk3588/models[0m
Predict:         yolo predict task=detect model=/workspace/yolo-rk3588/result/yolov8/yolov8.onnx imgsz=640  
Validate:        yolo val task=detect model=/workspace/yolo-rk3588/result/yolov8/yolov8.onnx imgsz=640 data=coco.yaml  
Visualize:       https://netron.app
