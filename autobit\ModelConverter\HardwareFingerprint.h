#ifndef HARDWAREFINGERPRINT_H
#define HARDWAREFINGERPRINT_H

#include <QString>
#include <QCryptographicHash>
#include <QSysInfo>
#include <QNetworkInterface>
#include <QStorageInfo>
#include <QDir>
#include <QDebug>

/**
 * @brief 硬件指纹生成器
 * 
 * 用于生成设备唯一标识，基于多种硬件信息组合
 * 支持Linux和Windows平台
 */
class HardwareFingerprint
{
public:
    /**
     * @brief 获取设备硬件指纹
     * @return 32位十六进制字符串
     */
    static QString getDeviceFingerprint();
    
    /**
     * @brief 获取CPU信息
     * @return CPU序列号或型号信息
     */
    static QString getCpuInfo();
    
    /**
     * @brief 获取主板信息
     * @return 主板序列号或型号信息
     */
    static QString getMotherboardInfo();
    
    /**
     * @brief 获取MAC地址
     * @return 第一个有效网卡的MAC地址
     */
    static QString getMacAddress();
    
    /**
     * @brief 获取硬盘序列号
     * @return 系统盘序列号
     */
    static QString getDiskSerial();
    
    /**
     * @brief 获取系统信息
     * @return 系统版本和架构信息
     */
    static QString getSystemInfo();
    
    /**
     * @brief 验证设备指纹是否匹配
     * @param storedFingerprint 存储的指纹
     * @param currentFingerprint 当前指纹
     * @return 是否匹配
     */
    static bool verifyFingerprint(const QString &storedFingerprint, 
                                 const QString &currentFingerprint = QString());

private:
    /**
     * @brief 执行系统命令并获取输出
     * @param command 命令
     * @return 命令输出
     */
    static QString executeCommand(const QString &command);
    
    /**
     * @brief 清理字符串（移除空白字符和特殊字符）
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    static QString cleanString(const QString &input);
    
    /**
     * @brief 生成MD5哈希
     * @param input 输入字符串
     * @return MD5哈希值
     */
    static QString generateMD5(const QString &input);
};

#endif // HARDWAREFINGERPRINT_H
