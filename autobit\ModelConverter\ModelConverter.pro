# AutoBit 模型转换工具 - Qt项目配置
# 更新时间: 2024-06-29
# 支持双模式激活系统和现代化UI

# 指定要使用的 Qt 安装前缀
QMAKE_PREFIX_PATH = /opt/Qt5.12.6/5.12.6/gcc_64

QT       += core gui widgets network
TARGET   = ModelConverter
CONFIG   += c++17

# 编译器选项
QMAKE_CXXFLAGS += -fno-sized-deallocation

# 预处理器定义
DEFINES += SF_CORE_LS_EXPORT=Q_DECL_IMPORT

# 条件编译支持
CONFIG(debug, debug|release) {
    DEFINES += DEBUG_MODE
    TARGET = ModelConverter_debug
} else {
    DEFINES += RELEASE_MODE
}

# Qt库链接
LIBS += -L$$QMAKE_PREFIX_PATH/lib \
        -lQt5Widgets \
        -lQt5Gui \
        -lQt5Core \
        -lQt5Network

# 第三方库链接（许可证库）
LIBS += -L$$PWD -lsf-core-ls

# 运行时库路径
QMAKE_RPATHDIR += $$QMAKE_PREFIX_PATH/lib \
                  $$PWD

# 资源文件
RESOURCES += ModelConverter.qrc

# 源文件
SOURCES  += main.cpp \
            activationwindow.cpp \
            conversionwindow.cpp \
            HardwareFingerprint.cpp \
            LicenseManager.cpp \
            sized_delete.cpp

# 头文件
HEADERS  += activationwindow.h \
            conversionwindow.h \
            HardwareFingerprint.h \
            LicenseManager.h \
            SFCoreIntf.h \
            YXPermission.h \
            yxcppsdkdishcore_global.h

# 安装配置
target.path = $$PWD/build
INSTALLS += target

# 清理配置
QMAKE_CLEAN += $$TARGET

# 编译信息
message("Qt版本: $$[QT_VERSION]")
message("Qt路径: $$QMAKE_PREFIX_PATH")
message("目标文件: $$TARGET")
message("编译模式: $$CONFIG")