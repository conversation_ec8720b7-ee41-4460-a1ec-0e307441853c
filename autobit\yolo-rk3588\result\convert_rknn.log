I rknn-toolkit2 version: 2.3.2
--> Config model
done
--> Loading model

I Loading :   0%|                                                           | 0/134 [00:00<?, ?it/s]
I Loading : 100%|██████████████████████████████████████████████| 134/134 [00:00<00:00, 35055.00it/s]
done
--> Building model

I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   1%|▍                                              | 1/100 [00:00<00:00, 1165.41it/s]
I OpFusing 0:   2%|▉                                              | 2/100 [00:00<00:00, 1581.56it/s]
I OpFusing 0:   3%|█▍                                              | 3/100 [00:00<00:00, 139.96it/s]
I OpFusing 0:   4%|█▉                                              | 4/100 [00:00<00:00, 183.17it/s]
I OpFusing 0:   6%|██▉                                             | 6/100 [00:00<00:00, 265.34it/s]
I OpFusing 0:   7%|███▎                                            | 7/100 [00:00<00:00, 304.16it/s]
I OpFusing 0:   8%|███▊                                            | 8/100 [00:00<00:00, 341.73it/s]
I OpFusing 0:  10%|████▋                                          | 10/100 [00:00<00:00, 413.96it/s]
I OpFusing 0:  11%|█████▏                                         | 11/100 [00:00<00:00, 447.94it/s]
I OpFusing 0:  12%|█████▋                                         | 12/100 [00:00<00:00, 477.78it/s]
I OpFusing 0:  13%|██████                                         | 13/100 [00:00<00:00, 509.61it/s]
I OpFusing 0:  15%|███████                                        | 15/100 [00:00<00:00, 571.82it/s]
I OpFusing 0:  16%|███████▌                                       | 16/100 [00:00<00:00, 600.77it/s]
I OpFusing 0:  17%|███████▉                                       | 17/100 [00:00<00:00, 629.04it/s]
I OpFusing 0:  19%|████████▉                                      | 19/100 [00:00<00:00, 684.43it/s]
I OpFusing 0:  20%|█████████▍                                     | 20/100 [00:00<00:00, 710.35it/s]
I OpFusing 0:  22%|██████████▎                                    | 22/100 [00:00<00:00, 766.36it/s]
I OpFusing 0:  23%|██████████▊                                    | 23/100 [00:00<00:00, 790.46it/s]
I OpFusing 0:  25%|███████████▊                                   | 25/100 [00:00<00:00, 838.39it/s]
I OpFusing 0:  26%|████████████▏                                  | 26/100 [00:00<00:00, 860.45it/s]
I OpFusing 0:  27%|████████████▋                                  | 27/100 [00:00<00:00, 882.18it/s]
I OpFusing 0:  29%|█████████████▋                                 | 29/100 [00:00<00:00, 925.35it/s]
I OpFusing 0:  30%|██████████████                                 | 30/100 [00:00<00:00, 945.57it/s]
I OpFusing 0:  32%|███████████████                                | 32/100 [00:00<00:00, 985.17it/s]
I OpFusing 0:  33%|███████████████▏                              | 33/100 [00:00<00:00, 1003.94it/s]
I OpFusing 0:  37%|█████████████████                             | 37/100 [00:00<00:00, 1093.09it/s]
I OpFusing 0:  39%|█████████████████▉                            | 39/100 [00:00<00:00, 1130.13it/s]
I OpFusing 0:  41%|██████████████████▊                           | 41/100 [00:00<00:00, 1163.56it/s]
I OpFusing 0:  42%|███████████████████▎                          | 42/100 [00:00<00:00, 1179.06it/s]
I OpFusing 0:  44%|████████████████████▏                         | 44/100 [00:00<00:00, 1216.66it/s]
I OpFusing 0:  46%|█████████████████████▏                        | 46/100 [00:00<00:00, 1250.12it/s]
I OpFusing 0:  48%|██████████████████████                        | 48/100 [00:00<00:00, 1279.62it/s]
I OpFusing 0:  50%|███████████████████████                       | 50/100 [00:00<00:00, 1319.46it/s]
I OpFusing 0:  51%|███████████████████████▍                      | 51/100 [00:00<00:00, 1326.93it/s]
I OpFusing 0:  53%|████████████████████████▍                     | 53/100 [00:00<00:00, 1365.07it/s]
I OpFusing 0:  55%|█████████████████████████▎                    | 55/100 [00:00<00:00, 1397.11it/s]
I OpFusing 0:  57%|██████████████████████████▏                   | 57/100 [00:00<00:00, 1421.87it/s]
I OpFusing 0:  58%|██████████████████████████▋                   | 58/100 [00:00<00:00, 1433.12it/s]
I OpFusing 0:  60%|███████████████████████████▌                  | 60/100 [00:00<00:00, 1462.87it/s]
I OpFusing 0:  62%|████████████████████████████▌                 | 62/100 [00:00<00:00, 1497.65it/s]
I OpFusing 0:  64%|█████████████████████████████▍                | 64/100 [00:00<00:00, 1522.94it/s]
I OpFusing 0:  66%|██████████████████████████████▎               | 66/100 [00:00<00:00, 1544.22it/s]
I OpFusing 0:  67%|██████████████████████████████▊               | 67/100 [00:00<00:00, 1553.87it/s]
I OpFusing 0:  69%|███████████████████████████████▋              | 69/100 [00:00<00:00, 1579.33it/s]
I OpFusing 0:  71%|████████████████████████████████▋             | 71/100 [00:00<00:00, 1610.99it/s]
I OpFusing 0:  73%|█████████████████████████████████▌            | 73/100 [00:00<00:00, 1642.30it/s]
I OpFusing 0:  75%|██████████████████████████████████▌           | 75/100 [00:00<00:00, 1667.82it/s]
I OpFusing 0:  76%|██████████████████████████████████▉           | 76/100 [00:00<00:00, 1676.05it/s]
I OpFusing 0:  80%|████████████████████████████████████▊         | 80/100 [00:00<00:00, 1734.48it/s]
I OpFusing 0:  82%|█████████████████████████████████████▋        | 82/100 [00:00<00:00, 1763.38it/s]
I OpFusing 0:  84%|██████████████████████████████████████▋       | 84/100 [00:00<00:00, 1786.57it/s]
I OpFusing 0:  86%|███████████████████████████████████████▌      | 86/100 [00:00<00:00, 1814.76it/s]
I OpFusing 0:  90%|█████████████████████████████████████████▍    | 90/100 [00:00<00:00, 1868.87it/s]
I OpFusing 0:  92%|██████████████████████████████████████████▎   | 92/100 [00:00<00:00, 1895.87it/s]
I OpFusing 0:  95%|███████████████████████████████████████████▋  | 95/100 [00:00<00:00, 1937.31it/s]
I OpFusing 0:  96%|████████████████████████████████████████████▏ | 96/100 [00:00<00:00, 1943.03it/s]
I OpFusing 0: 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 1189.93it/s]
I OpFusing 1 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 1 :  82%|█████████████████████████████████████▋        | 82/100 [00:00<00:00, 725.85it/s]
I OpFusing 1 :  90%|█████████████████████████████████████████▍    | 90/100 [00:00<00:00, 781.77it/s]
I OpFusing 1 :  98%|█████████████████████████████████████████████ | 98/100 [00:00<00:00, 836.02it/s]
I OpFusing 1 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 662.82it/s]
I OpFusing 0 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 0 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 577.82it/s]
I OpFusing 1 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 1 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 514.80it/s]
I OpFusing 2 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 502.10it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 208.81it/s]
[1;33mW[0m [1;33mbuild: found outlier value, this may affect quantization accuracy
                        const name                        abs_mean    abs_std     outlier value
                        model.0.conv.weight               2.44        2.47        -17.494     
                        model.22.cv3.2.1.conv.weight      0.09        0.14        -10.215     
                        model.22.cv3.1.1.conv.weight      0.12        0.19        13.361, 13.317
                        model.22.cv3.0.1.conv.weight      0.18        0.20        -11.216     [0m





I GraphPreparing :   0%|                                                    | 0/161 [00:00<?, ?it/s]
I GraphPreparing : 100%|███████████████████████████████████████| 161/161 [00:00<00:00, 10589.19it/s]

I Quantizating :   0%|                                                      | 0/161 [00:00<?, ?it/s]
I Quantizating :   1%|▎                                             | 1/161 [00:00<00:49,  3.21it/s]
I Quantizating :   1%|▌                                             | 2/161 [00:00<00:45,  3.53it/s]
I Quantizating :   2%|█▏                                            | 4/161 [00:00<00:27,  5.79it/s]
I Quantizating :   4%|█▋                                            | 6/161 [00:00<00:19,  7.97it/s]
I Quantizating :   6%|██▌                                           | 9/161 [00:01<00:12, 11.93it/s]
I Quantizating :   7%|███                                          | 11/161 [00:01<00:10, 13.64it/s]
I Quantizating :   9%|████▏                                        | 15/161 [00:01<00:09, 15.61it/s]
I Quantizating :  11%|████▊                                        | 17/161 [00:01<00:09, 15.86it/s]
I Quantizating :  13%|█████▊                                       | 21/161 [00:01<00:06, 20.56it/s]
I Quantizating :  16%|███████▎                                     | 26/161 [00:01<00:05, 25.94it/s]
I Quantizating :  19%|████████▋                                    | 31/161 [00:01<00:04, 30.25it/s]
I Quantizating :  22%|█████████▊                                   | 35/161 [00:02<00:04, 27.97it/s]
I Quantizating :  25%|███████████▍                                 | 41/161 [00:02<00:03, 34.46it/s]
I Quantizating :  30%|█████████████▍                               | 48/161 [00:02<00:02, 43.06it/s]
I Quantizating :  33%|██████████████▊                              | 53/161 [00:02<00:02, 42.07it/s]
I Quantizating :  39%|█████████████████▌                           | 63/161 [00:02<00:01, 55.78it/s]
I Quantizating :  46%|████████████████████▋                        | 74/161 [00:02<00:01, 69.95it/s]
I Quantizating :  51%|██████████████████████▉                      | 82/161 [00:02<00:01, 63.09it/s]
I Quantizating :  55%|████████████████████████▉                    | 89/161 [00:02<00:01, 50.45it/s]
I Quantizating :  59%|██████████████████████████▌                  | 95/161 [00:03<00:01, 46.29it/s]
I Quantizating :  63%|███████████████████████████▌                | 101/161 [00:03<00:01, 44.08it/s]
I Quantizating :  67%|█████████████████████████████▌              | 108/161 [00:03<00:01, 48.18it/s]
I Quantizating :  72%|███████████████████████████████▋            | 116/161 [00:03<00:00, 53.28it/s]
I Quantizating :  77%|█████████████████████████████████▉          | 124/161 [00:03<00:00, 59.66it/s]
I Quantizating :  84%|█████████████████████████████████████▏      | 136/161 [00:03<00:00, 71.51it/s]
I Quantizating :  89%|███████████████████████████████████████▎    | 144/161 [00:03<00:00, 59.35it/s]
I Quantizating :  94%|█████████████████████████████████████████▎  | 151/161 [00:04<00:00, 44.84it/s]
I Quantizating :  98%|██████████████████████████████████████████▉ | 157/161 [00:04<00:00, 27.38it/s]
I Quantizating : 100%|████████████████████████████████████████████| 161/161 [00:04<00:00, 34.17it/s]
[1;33mW[0m [1;33mbuild: The default input dtype of 'images' is changed from 'float32' to 'int8' in rknn model for performance!
                       Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '317' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '325' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '331' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '338' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '346' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '352' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '359' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '367' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '373' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
I rknn building ...
I rknn building done.
done
--> Export rknn model
done
