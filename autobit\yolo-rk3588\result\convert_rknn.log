I rknn-toolkit2 version: 2.3.2
--> Config model
done
--> Loading model

I Loading :   0%|                                                           | 0/123 [00:00<?, ?it/s]
I Loading : 100%|██████████████████████████████████████████████| 123/123 [00:00<00:00, 19269.39it/s]
done
--> Building model

I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   0%|                                                         | 0/100 [00:00<?, ?it/s]
I OpFusing 0:   1%|▍                                               | 1/100 [00:00<00:00, 628.64it/s]
I OpFusing 0:   2%|▉                                               | 2/100 [00:00<00:00, 846.74it/s]
I OpFusing 0:   3%|█▍                                              | 3/100 [00:00<00:00, 966.58it/s]
I OpFusing 0:   4%|█▉                                             | 4/100 [00:00<00:00, 1030.79it/s]
I OpFusing 0:   6%|██▊                                            | 6/100 [00:00<00:00, 1237.20it/s]
I OpFusing 0:   8%|███▊                                           | 8/100 [00:00<00:00, 1373.27it/s]
I OpFusing 0:   9%|████▏                                          | 9/100 [00:00<00:00, 1376.39it/s]
I OpFusing 0:  10%|████▌                                         | 10/100 [00:00<00:00, 1364.84it/s]
I OpFusing 0:  11%|█████                                         | 11/100 [00:00<00:00, 1367.19it/s]
I OpFusing 0:  12%|█████▌                                        | 12/100 [00:00<00:00, 1384.18it/s]
I OpFusing 0:  13%|█████▉                                        | 13/100 [00:00<00:00, 1364.85it/s]
I OpFusing 0:  15%|██████▉                                       | 15/100 [00:00<00:00, 1471.34it/s]
I OpFusing 0:  16%|███████▎                                      | 16/100 [00:00<00:00, 1453.67it/s]
I OpFusing 0:  18%|████████▎                                     | 18/100 [00:00<00:00, 1512.58it/s]
I OpFusing 0:  19%|████████▋                                     | 19/100 [00:00<00:00, 1517.27it/s]
I OpFusing 0:  20%|█████████▏                                    | 20/100 [00:00<00:00, 1519.40it/s]
I OpFusing 0:  21%|█████████▋                                    | 21/100 [00:00<00:00, 1527.69it/s]
I OpFusing 0:  22%|██████████                                    | 22/100 [00:00<00:00, 1529.09it/s]
I OpFusing 0:  24%|███████████                                   | 24/100 [00:00<00:00, 1586.20it/s]
I OpFusing 0:  25%|███████████▌                                  | 25/100 [00:00<00:00, 1594.53it/s]
I OpFusing 0:  27%|████████████▍                                 | 27/100 [00:00<00:00, 1607.06it/s]
I OpFusing 0:  28%|████████████▉                                 | 28/100 [00:00<00:00, 1613.33it/s]
I OpFusing 0:  30%|█████████████▊                                | 30/100 [00:00<00:00, 1657.02it/s]
I OpFusing 0:  32%|██████████████▋                               | 32/100 [00:00<00:00, 1666.18it/s]
I OpFusing 0:  33%|███████████████▏                              | 33/100 [00:00<00:00, 1663.23it/s]
I OpFusing 0:  35%|████████████████                              | 35/100 [00:00<00:00, 1711.34it/s]
I OpFusing 0:  36%|████████████████▌                             | 36/100 [00:00<00:00, 1714.70it/s]
I OpFusing 0:  37%|█████████████████                             | 37/100 [00:00<00:00, 1715.82it/s]
I OpFusing 0:  39%|█████████████████▉                            | 39/100 [00:00<00:00, 1747.81it/s]
I OpFusing 0:  41%|██████████████████▊                           | 41/100 [00:00<00:00, 1752.97it/s]
I OpFusing 0:  43%|███████████████████▊                          | 43/100 [00:00<00:00, 1791.12it/s]
I OpFusing 0:  48%|██████████████████████                        | 48/100 [00:00<00:00, 1840.68it/s]
I OpFusing 0:  50%|███████████████████████                       | 50/100 [00:00<00:00, 1874.87it/s]
I OpFusing 0:  52%|███████████████████████▉                      | 52/100 [00:00<00:00, 1872.52it/s]
I OpFusing 0:  54%|████████████████████████▊                     | 54/100 [00:00<00:00, 1900.82it/s]
I OpFusing 0:  55%|█████████████████████████▎                    | 55/100 [00:00<00:00, 1895.46it/s]
I OpFusing 0:  57%|██████████████████████████▏                   | 57/100 [00:00<00:00, 1915.56it/s]
I OpFusing 0:  59%|███████████████████████████▏                  | 59/100 [00:00<00:00, 1930.94it/s]
I OpFusing 0:  60%|███████████████████████████▌                  | 60/100 [00:00<00:00, 1920.09it/s]
I OpFusing 0:  63%|████████████████████████████▉                 | 63/100 [00:00<00:00, 1963.32it/s]
I OpFusing 0:  65%|█████████████████████████████▉                | 65/100 [00:00<00:00, 1988.66it/s]
I OpFusing 0:  66%|██████████████████████████████▎               | 66/100 [00:00<00:00, 1979.87it/s]
I OpFusing 0:  68%|███████████████████████████████▎              | 68/100 [00:00<00:00, 2001.37it/s]
I OpFusing 0:  70%|████████████████████████████████▏             | 70/100 [00:00<00:00, 2007.56it/s]
I OpFusing 0:  72%|█████████████████████████████████             | 72/100 [00:00<00:00, 2031.40it/s]
I OpFusing 0:  74%|██████████████████████████████████            | 74/100 [00:00<00:00, 2046.42it/s]
I OpFusing 0:  76%|██████████████████████████████████▉           | 76/100 [00:00<00:00, 2069.71it/s]
I OpFusing 0:  78%|███████████████████████████████████▉          | 78/100 [00:00<00:00, 2091.35it/s]
I OpFusing 0:  80%|████████████████████████████████████▊         | 80/100 [00:00<00:00, 2113.85it/s]
I OpFusing 0:  82%|█████████████████████████████████████▋        | 82/100 [00:00<00:00, 2124.60it/s]
I OpFusing 0:  84%|██████████████████████████████████████▋       | 84/100 [00:00<00:00, 2148.50it/s]
I OpFusing 0:  86%|███████████████████████████████████████▌      | 86/100 [00:00<00:00, 2157.27it/s]
I OpFusing 0:  88%|████████████████████████████████████████▍     | 88/100 [00:00<00:00, 2176.78it/s]
I OpFusing 0:  90%|█████████████████████████████████████████▍    | 90/100 [00:00<00:00, 2199.23it/s]
I OpFusing 0:  92%|██████████████████████████████████████████▎   | 92/100 [00:00<00:00, 2209.26it/s]
I OpFusing 0:  95%|███████████████████████████████████████████▋  | 95/100 [00:00<00:00, 2229.38it/s]
I OpFusing 0: 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 1247.64it/s]
I OpFusing 1 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 1 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 967.83it/s]
I OpFusing 2 :   0%|                                                        | 0/100 [00:00<?, ?it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 903.70it/s]
I OpFusing 2 : 100%|█████████████████████████████████████████████| 100/100 [00:00<00:00, 150.97it/s]
[1;33mW[0m [1;33mbuild: found outlier value, this may affect quantization accuracy
                        const nameabs_mean    abs_std     outlier value
                        344      0.83        1.39        14.282      [0m



I GraphPreparing :   0%|                                                    | 0/149 [00:00<?, ?it/s]
I GraphPreparing : 100%|████████████████████████████████████████| 149/149 [00:00<00:00, 9866.93it/s]

I Quantizating :   0%|                                                      | 0/149 [00:00<?, ?it/s]
I Quantizating :   1%|▎                                             | 1/149 [00:00<00:32,  4.49it/s]
I Quantizating :   1%|▌                                             | 2/149 [00:00<00:47,  3.09it/s]
I Quantizating :   2%|▉                                             | 3/149 [00:01<01:27,  1.66it/s]
I Quantizating :   3%|█▏                                            | 4/149 [00:01<01:12,  2.00it/s]
I Quantizating :   3%|█▌                                            | 5/149 [00:02<01:08,  2.10it/s]
I Quantizating :   5%|██▏                                           | 7/149 [00:02<00:40,  3.48it/s]
I Quantizating :   6%|██▊                                           | 9/149 [00:02<00:28,  4.99it/s]
I Quantizating :   7%|███                                          | 10/149 [00:02<00:26,  5.33it/s]
I Quantizating :   7%|███▎                                         | 11/149 [00:02<00:24,  5.68it/s]
I Quantizating :   9%|████▏                                        | 14/149 [00:03<00:16,  8.32it/s]
I Quantizating :  11%|████▊                                        | 16/149 [00:03<00:13, 10.06it/s]
I Quantizating :  12%|█████▍                                       | 18/149 [00:03<00:19,  6.65it/s]
I Quantizating :  13%|█████▋                                       | 19/149 [00:03<00:18,  6.91it/s]
I Quantizating :  15%|██████▋                                      | 22/149 [00:04<00:12, 10.25it/s]
I Quantizating :  16%|███████▏                                     | 24/149 [00:04<00:12, 10.23it/s]
I Quantizating :  18%|████████▏                                    | 27/149 [00:04<00:08, 13.75it/s]
I Quantizating :  19%|████████▊                                    | 29/149 [00:04<00:09, 12.33it/s]
I Quantizating :  21%|█████████▋                                   | 32/149 [00:04<00:07, 15.18it/s]
I Quantizating :  23%|██████████▌                                  | 35/149 [00:04<00:06, 16.39it/s]
I Quantizating :  25%|███████████▏                                 | 37/149 [00:05<00:09, 11.24it/s]
I Quantizating :  26%|███████████▊                                 | 39/149 [00:05<00:08, 12.38it/s]
I Quantizating :  29%|████████████▉                                | 43/149 [00:05<00:07, 14.99it/s]
I Quantizating :  32%|██████████████▍                              | 48/149 [00:05<00:05, 18.36it/s]
I Quantizating :  36%|████████████████                             | 53/149 [00:05<00:04, 20.22it/s]
I Quantizating :  39%|█████████████████▌                           | 58/149 [00:05<00:03, 25.49it/s]
I Quantizating :  41%|██████████████████▍                          | 61/149 [00:06<00:05, 17.54it/s]
I Quantizating :  44%|███████████████████▉                         | 66/149 [00:06<00:03, 22.33it/s]
I Quantizating :  46%|████████████████████▊                        | 69/149 [00:06<00:03, 22.45it/s]
I Quantizating :  50%|██████████████████████▎                      | 74/149 [00:06<00:02, 26.14it/s]
I Quantizating :  56%|█████████████████████████▎                   | 84/149 [00:06<00:01, 36.36it/s]
I Quantizating :  60%|██████████████████████████▉                  | 89/149 [00:06<00:01, 39.04it/s]
I Quantizating :  63%|████████████████████████████▍                | 94/149 [00:07<00:01, 31.51it/s]
I Quantizating :  66%|█████████████████████████████▌               | 98/149 [00:07<00:01, 32.04it/s]
I Quantizating :  68%|██████████████████████████████              | 102/149 [00:07<00:01, 29.62it/s]
I Quantizating :  71%|███████████████████████████████▎            | 106/149 [00:07<00:01, 27.01it/s]
I Quantizating :  73%|████████████████████████████████▏           | 109/149 [00:07<00:01, 23.16it/s]
I Quantizating :  75%|█████████████████████████████████           | 112/149 [00:08<00:01, 21.25it/s]
I Quantizating :  77%|█████████████████████████████████▉          | 115/149 [00:08<00:01, 18.36it/s]
I Quantizating :  79%|██████████████████████████████████▌         | 117/149 [00:08<00:01, 17.06it/s]
I Quantizating :  82%|████████████████████████████████████        | 122/149 [00:08<00:01, 22.68it/s]
I Quantizating :  84%|████████████████████████████████████▉       | 125/149 [00:08<00:01, 21.58it/s]
I Quantizating :  87%|██████████████████████████████████████      | 129/149 [00:08<00:00, 23.17it/s]
I Quantizating :  89%|██████████████████████████████████████▉     | 132/149 [00:08<00:00, 23.36it/s]
I Quantizating :  92%|████████████████████████████████████████▍   | 137/149 [00:09<00:00, 24.73it/s]
I Quantizating :  95%|█████████████████████████████████████████▉  | 142/149 [00:09<00:00, 29.43it/s]
I Quantizating :  98%|███████████████████████████████████████████ | 146/149 [00:09<00:00, 30.53it/s]
I Quantizating : 100%|████████████████████████████████████████████| 149/149 [00:09<00:00, 15.49it/s]
[1;33mW[0m [1;33mbuild: The default input dtype of 'images' is changed from 'float32' to 'int8' in rknn model for performance!
                       Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of 'output0' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '340' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
[1;33mW[0m [1;33mbuild: The default output dtype of '342' is changed from 'float32' to 'int8' in rknn model for performance!
                      Please take care of this change when deploy rknn model with Runtime API![0m
I rknn building ...
I rknn building done.
done
--> Export rknn model
done
